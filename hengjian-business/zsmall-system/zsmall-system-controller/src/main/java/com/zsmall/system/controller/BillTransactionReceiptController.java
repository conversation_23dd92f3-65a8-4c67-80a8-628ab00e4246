package com.zsmall.system.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.system.mapper.SysTenantMapper;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.system.biz.service.BillHeadSupper;
import com.zsmall.system.biz.service.IBillTransactionReceiptService;
import com.zsmall.system.entity.domain.BillTransactionReceiptSelectDTO;
import com.zsmall.system.entity.domain.bo.billTransactionReceipt.BillTransactionReceiptBo;
import com.zsmall.system.entity.domain.vo.billTransactionReceipt.BillTransactionReceiptVo;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import java.io.BufferedOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 钱包账单
 *
 * <AUTHOR> Li
 * @date 2024-09-24
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/billTransactionReceipt")
public class BillTransactionReceiptController extends BaseController {

    private final IBillTransactionReceiptService billTransactionReceiptService;
    private final SysTenantMapper sysTenantMapper;
    private final BillHeadSupper billHeadSupper;

    /**
     * 查询钱包账单列表
     */
    @GetMapping("/list")
    public TableDataInfo<BillTransactionReceiptVo> list(BillTransactionReceiptSelectDTO dto, PageQuery pageQuery) {
        return billTransactionReceiptService.queryPageList(dto, pageQuery);
    }

    /**
     * 导出钱包账单列表
     */
    @Log(title = "钱包账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R export(@RequestBody BillTransactionReceiptSelectDTO dto, HttpServletResponse response) {
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.BILL_TRANSACTION_RECEIPT_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.BillTransactionReceiptExport, tempFileSavePath -> {
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(0);
            pageQuery.setPageSize(Integer.MAX_VALUE);
        List<BillTransactionReceiptVo> list = billTransactionReceiptService.queryList(dto);
        list.forEach(s->{
            s.setBillTime(DateUtil.format(s.getBillStartTime(), "yyyy-MM-dd HH:mm:ss")
                +" 至 "+DateUtil.format(s.getBillEndTime(), "yyyy-MM-dd HH:mm:ss"));
        });
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelUtil.exportExcelWithLocale(list, "BillTransactionReceipt", BillTransactionReceiptVo.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();

    }

    /**
     * 获取钱包账单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:transactionReceipt:query")
    @GetMapping("/{id}")
    public R<BillTransactionReceiptVo> getInfo(@NotNull(message = "主键不能为空")
                                               @PathVariable Long id) {
        return R.ok(billTransactionReceiptService.queryById(id));
    }

    /**
     * 新增钱包账单
     */
    @SaCheckPermission("system:transactionReceipt:add")
    @Log(title = "钱包账单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BillTransactionReceiptBo bo) {
        return toAjax(billTransactionReceiptService.insertByBo(bo));
    }

    /**
     * 修改钱包账单
     */
    @SaCheckPermission("system:transactionReceipt:edit")
    @Log(title = "钱包账单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BillTransactionReceiptBo bo) {
        return toAjax(billTransactionReceiptService.updateByBo(bo));
    }

    /**
     * 删除钱包账单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:transactionReceipt:remove")
    @Log(title = "钱包账单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(billTransactionReceiptService.deleteWithValidByIds(List.of(ids), true));
    }
    /**
     * @description: 分销商钱包总览
     * @author: len
     *  @date: 2024/9/1 17:05
     * @param: tenantId
     * @return: com.hengjian.common.core.domain.R<java.lang.Void>
     **/
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/generatedTransactionReceipt")
    public void generatedTransactionReceipt(@RequestParam String tenantId ,String startTimeStr, String endTimeStr,String startTime,String endTime) {
        try {
            List<String> tenantIDs=new ArrayList<>();
            if (StringUtils.isNotEmpty(tenantId)){
                tenantIDs=List.of(tenantId);
            }else {
                //查询分销商
                 tenantIDs =  TenantHelper.ignore(()->sysTenantMapper.getAllApprovedTenant(TenantType.Distributor.name()));
            }
            for (Object tenantIds : tenantIDs) {
                billHeadSupper.generatedTransactionReceipt(tenantIds,false,startTimeStr,endTimeStr,startTime,endTime);
                log.info("处理分销商：{}，分销商钱包总览任务执行完成",tenantIds);
            }
            log.info(StrUtil.format("分销商钱包总览生成成功,开始时间:{},结束时间：{}",startTime,endTime));
        }catch (Exception e){
            log.error("分销商钱包总览生成失败：{}",e.getMessage());
            throw new RuntimeException(e);
        }

    }
}
