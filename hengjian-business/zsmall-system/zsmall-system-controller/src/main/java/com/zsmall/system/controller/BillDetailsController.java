package com.zsmall.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.system.mapper.SysTenantMapper;
import com.zsmall.system.biz.service.BillHeadSupper;
import com.zsmall.system.biz.service.IBillDetailsService;
import com.zsmall.system.entity.domain.Bill;
import com.zsmall.system.entity.domain.bo.billDatails.BillDetailsBo;
import com.zsmall.system.entity.domain.vo.billDetails.BillDetailsVo;
import com.zsmall.system.entity.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 账单明细-new
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/billDetails")
public class BillDetailsController extends BaseController {

    private final IBillDetailsService billDetailsService;
    private final SysTenantMapper sysTenantMapper;
    private final IBillDetailsService billDetailService;
    private final BillMapper billMapper;
    private final BillHeadMapper billHeadMapper;
    private final BillAbstractMapper billAbstractMapper;
    private final BillAbstractDetailMapper billAbstractDetailMapper;
    private final BillRelationMapper billRelationMapper;
    private final BillRelationDetailMapper billRelationDetailMapper;
    private final BillDetailsMapper billDetailsMapper;
    private final BillHeadSupper billHeadSupper;

    /**
     * 查询账单明细-new列表
     */
    @SaCheckPermission("system:details:list")
    @GetMapping("/list")
    public TableDataInfo<BillDetailsVo> list(BillDetailsBo bo, PageQuery pageQuery) {
        return billDetailsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出账单
     */
    @SaCheckPermission("system:details:export")
    @Log(title = "账单明细-new", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BillDetailsBo bo, HttpServletResponse response) {
        List<BillDetailsVo> list = billDetailsService.queryList(bo);
        ExcelUtil.exportExcel(list, "账单明细-new", BillDetailsVo.class, response,false);
    }

    /**
     * 获取账单明细-new详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:details:query")
    @GetMapping("/{id}")
    public R<BillDetailsVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(billDetailsService.queryById(id));
    }

    /**
     * 新增账单明细-new
     */
    @SaCheckPermission("system:details:add")
    @Log(title = "账单明细-new", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BillDetailsBo bo) {
        return toAjax(billDetailsService.insertByBo(bo));
    }

    /**
     * 修改账单明细-new
     */
    @SaCheckPermission("system:details:edit")
    @Log(title = "账单明细-new", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BillDetailsBo bo) {
        return toAjax(billDetailsService.updateByBo(bo));
    }

    /**
     * 删除账单明细-new
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:details:remove")
    @Log(title = "账单明细-new", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(billDetailsService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * @description: 生成供应商账单信息(1号/16号)
     * @author: len
    *  @date: 2024/9/1 17:05
     * @param: tenantId 供应商租户id
     * @param: startTimeStr 开始时间月日
     * @param: endTimeStr 结束时间月日
     * @param: startTime 开始时间
     * @param: endTime 结束时间
     * @return: com.hengjian.common.core.domain.R
     **/
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/generatedBillBySupplierTenant")
    public void generatedBillBySupplierTenant(@RequestParam String tenantId ,String startTimeStr, String endTimeStr,String startTime,String endTime) {
//        int i = DateUtil.dayOfMonth(new Date());
//        if (i!=1 && i!=16) {
//            return null;
//        }
        try {
            List<String> tenantIDs = new ArrayList<>();
            if (StringUtils.isEmpty(tenantId)) {
                 tenantIDs =  TenantHelper.ignore(()->sysTenantMapper.getAllApprovedTenant(TenantType.Supplier.name()));
            } else {
                tenantIDs = List.of(tenantId);
            }
            for (Object tenantIds : tenantIDs) {
                billHeadSupper.generatedSupplierBillByTenant(tenantIds,false,startTimeStr,endTimeStr,startTime,endTime);
                log.info("处理供营商账单完成：{}",tenantIds);
            }
            log.info(StrUtil.format("供应商账单生成成功,开始时间:{},结束时间：{}",startTime,endTime));
        }catch (Exception e){
            log.error("供应商账单生成失败：{}",e.getMessage());
            throw new RuntimeException(e);
        }

    }

    /**
     * @description: 生成分销商账单信息 (每月1号)
     * @author: len
    *  @date: 2024/9/1 17:05
     * @param: tenantId
     * @return: com.hengjian.common.core.domain.R<java.lang.Void>
     **/
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/generatedBillByTenant")
    public R generatedBillByTenant(@RequestParam String tenantId ,String startTimeStr, String endTimeStr,String startTime,String endTime) {
        // int i = DateUtil.dayOfMonth(new Date());
        try {
            List<String> tenantIDs=new ArrayList<>();
            if (StringUtils.isNotEmpty(tenantId)){
                tenantIDs=List.of(tenantId);
            }else {
                tenantIDs =  TenantHelper.ignore(()->sysTenantMapper.getAllApprovedTenant(TenantType.Distributor.name()));
            }
            for (Object tenantIds : tenantIDs) {
                billHeadSupper.generatedDistributorBillByTenant(tenantIds,false,startTimeStr,endTimeStr,startTime,endTime);
                log.info("处理分销商：{}，账单完成",tenantIds);
            }
            log.info(StrUtil.format("分销商账单生成成功,开始时间:{},结束时间：{}",startTime,endTime));
            return R.ok(StrUtil.format("分销商账单生成成功,开始时间:{},结束时间：{}",startTime,endTime));
        }catch (Exception e){
            log.error("分销商生成账单失败：{}",e.getMessage());
            throw new RuntimeException(e);
        }
    }




    @Log(title = "处理供应商历史数据", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/dealSupplierTenantOldDate")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> dealOldDate(@RequestParam String billNo) {
        List<Object> objects=new ArrayList<>();
        if (StringUtils.isEmpty(billNo)){
            //查询所有的
            LambdaQueryWrapper<Bill> bill = new LambdaQueryWrapper<>();
            bill.select(Bill::getBillNo);
            objects =  TenantHelper.ignore(()->billMapper.selectObjs(bill));
        }else {
            objects=List.of(billNo);
        }
        for (Object object : objects) {
            billDetailService.dealBillDate(String.valueOf(object));
        }
        log.info("清洗所有供应商数据完成");
        return R.ok();
    }
}
