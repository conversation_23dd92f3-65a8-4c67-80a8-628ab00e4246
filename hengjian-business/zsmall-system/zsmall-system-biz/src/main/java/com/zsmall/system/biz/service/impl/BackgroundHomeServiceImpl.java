package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.service.DictService;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.order.entity.domain.vo.statistics.ChannelSalesStatisticsVo;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.product.entity.domain.vo.product.ProductNewestVo;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.system.biz.service.BackgroundHomeService;
import com.zsmall.system.entity.domain.vo.backgroundHome.BackgroundHomeInfoVo;
import com.zsmall.system.entity.domain.vo.backgroundHome.HomeProductVo;
import com.zsmall.system.entity.domain.vo.backgroundHome.TodayChannelSalesStatisticsVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 后台Home-业务实现
 *
 * <AUTHOR>
 * @date 2023/8/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BackgroundHomeServiceImpl implements BackgroundHomeService {

    private final IOrdersService iOrdersService;
    private final IProductService iProductService;

    private final DictService dictService;
    @Value("${spring.profiles.active}")
    private String env;
    /**
     * 查询后台Home信息
     */
    @Override
    public R<BackgroundHomeInfoVo> queryBackgroundHomeInfo() {

        LoginUser loginUser ;
        Date nowDate = new Date();
        DateTime startDate = DateUtil.beginOfDay(nowDate);
        DateTime endDate = DateUtil.endOfDay(nowDate);
        BackgroundHomeInfoVo vo = new BackgroundHomeInfoVo();
        TenantType tenantTypeEnum ;
        String tenantId ;
        if(!"dev".equals(env)){
            loginUser = LoginHelper.getLoginUser();
            tenantTypeEnum = loginUser.getTenantTypeEnum();
            tenantId = loginUser.getTenantId();
        }else{
            tenantTypeEnum = TenantType.Distributor;
            tenantId = "DLTA1X9";
        }

        List<ChannelSalesStatisticsVo> statisticsVoList;
        if (TenantType.Supplier.equals(tenantTypeEnum)) {
            statisticsVoList = iOrdersService.queryChannelSalesStatisticsForSup(tenantId, startDate, endDate);
        } else if (TenantType.Manager.equals(tenantTypeEnum)) {
            statisticsVoList = iOrdersService.queryChannelSalesStatisticsForMag(startDate, endDate);
        } else {
            statisticsVoList = iOrdersService.queryChannelSalesStatisticsForDis(startDate, endDate);
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageSize(3);
            List<ProductNewestVo> productNewestVoList = null;
//            loginUser != null &&
            if ((!ObjectUtil.equals(tenantId, "S0BJHUA") && !ObjectUtil.equals(tenantId, "D4ZQGHV"))) {
                // 看不到ZJHJ开头的商品
                productNewestVoList = iProductService.queryNewestProduct(pageQuery.build(),Boolean.TRUE);
            }else {
                productNewestVoList = iProductService.queryNewestProduct(pageQuery.build(),Boolean.FALSE);
            }
            List<HomeProductVo> newestProductList = BeanUtil.copyToList(productNewestVoList, HomeProductVo.class);
            vo.setNewestProductList(newestProductList);
        }

        List<TodayChannelSalesStatisticsVo> channelSalesList = BeanUtil.copyToList(statisticsVoList, TodayChannelSalesStatisticsVo.class);
        for (TodayChannelSalesStatisticsVo todayVo : channelSalesList) {
            vo.addSalesAmount(todayVo.getSalesAmount());
            vo.addOrderQuantity(todayVo.getOrderQuantity());
        }
        vo.setChannelSalesList(channelSalesList);
        return R.ok(vo);
    }

    /**
     * 查询静态文章
     *
     * @param type
     */
    @Override
    public R<String> queryStaticArticles(String type) {
        String lang = "en_US";

        String headerLanguage = ServletUtils.getHeaderLanguage();
        if (StrUtil.isNotBlank(headerLanguage)) {
            lang = headerLanguage;
        }

        String path = dictService.getDictValue("biz_static_articles_path", type + "_" + lang);
        String content = FileUtil.readString(path, StandardCharsets.UTF_8);
        R<String> ok = R.ok();
        ok.setData(content);
        return ok;
    }
}
