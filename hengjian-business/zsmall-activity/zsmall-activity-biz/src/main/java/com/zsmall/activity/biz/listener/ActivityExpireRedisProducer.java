package com.zsmall.activity.biz.listener;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.redis.utils.QueueUtils;
import com.hengjian.stream.mqProducer.constants.QueueConstants;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 活动过期Redis生产者
 * 基于Redis延时队列实现活动过期消息发送
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Component
@Slf4j
public class ActivityExpireRedisProducer {

    /**
     * 发送活动过期延时消息
     *
     * @param activeCode 活动编码
     * @param type 活动类型 1 供货商 2 分销商
     * @param endTime 结束时间
     */
    public void sendActivityExpireMessage(String activeCode, int type, Date endTime) {
        try {
            // 计算延时时间（毫秒）
            long delayTime = DateUtil.between(new Date(), endTime, DateUnit.MS, false);
            if (delayTime <= 0) {
                log.warn("[活动到期处理]，结束时间不能超过当前开始时间，不发送延时消息: {} - 结束时间: {}", activeCode, endTime);
                return;
            }

            // 构建消息对象
            ActivityExpireMq mq = new ActivityExpireMq();
            mq.setActiveCode(activeCode);
            mq.setType(type);

            // 序列化为JSON字符串发送到Redis延时队列
            String messageBody = JSONUtil.toJsonStr(mq);
            QueueUtils.addDelayedQueueObject(
                QueueConstants.QueueName.ACTIVITY_EXPIRE,
                messageBody,
                delayTime,
                TimeUnit.MILLISECONDS
            );

            log.info("发送活动过期延时消息成功: 活动编码={}, 活动类型={}, 到期时间={}, 延时={}ms",
                activeCode, type, DateUtil.formatDateTime(endTime), delayTime);
        } catch (Exception e) {
            log.error("发送活动过期消息失败: 活动编码={}, 活动类型={}", activeCode, type, e);
        }
    }
}
