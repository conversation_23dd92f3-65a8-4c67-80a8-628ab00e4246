package com.zsmall.activity.biz.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.redis.utils.QueueUtils;
import com.hengjian.stream.mqProducer.constants.QueueConstants;
import com.zsmall.activity.biz.service.ProductActiveService;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.Ordered;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 活动过期Redis消费者监听器
 * 基于Redis延时队列实现活动过期消息消费
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
public class ActivityExpireRedisConsumerListener implements ApplicationRunner, Ordered {

    @Autowired
    private ProductActiveService productActiveService;

    /**
     * 应用启动后初始化消费者监听
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        activityExpireConsumer();
        log.info("活动过期Redis消费者监听器启动成功");
    }

    /**
     * 设置执行顺序
     */
    @Override
    public int getOrder() {
        return -1;
    }

    /**
     * 活动过期消费者
     */
    private void activityExpireConsumer() {
        log.info("初始化活动过期消费者监听器");

        // 创建线程池处理消息
        ExecutorService executorService = Executors.newFixedThreadPool(2, r -> {
            Thread thread = new Thread(r);
            thread.setName("activity-expire-consumer-" + thread.getId());
            thread.setDaemon(true);
            return thread;
        });

        // 订阅Redis延时队列，接收JSON字符串
        QueueUtils.subscribeBlockingQueue(QueueConstants.QueueName.ACTIVITY_EXPIRE, (String messageBody) -> {
            log.info("【活动过期消费者】收到消息: {}", messageBody);

            try {
                // 反序列化消息
                ActivityExpireMq mq = JSONUtil.toBean(messageBody, ActivityExpireMq.class);

                // 验证消息内容
                if (ObjectUtil.isNull(mq)) {
                    log.error("【活动过期消费者】消息反序列化失败");
                    return;
                }

                if (StrUtil.isEmpty(mq.getActiveCode())) {
                    log.error("【活动过期消费者】活动编码为空");
                    return;
                }

                if (ObjectUtil.isNull(mq.getType())) {
                    log.error("【活动过期消费者】活动类型为空");
                    return;
                }

                // 检查线程池状态，避免任务堆积
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int queueSize = poolExecutor.getQueue().size();
                int activeCount = poolExecutor.getActiveCount();

                log.info("【活动过期消费者】线程池状态 - 队列大小: {}, 活跃线程: {}", queueSize, activeCount);

                // 异步处理活动过期逻辑
                executorService.execute(() -> {
                    try {
                        if (mq.getType() == 1) {
                            // 处理供应商活动过期
                            productActiveService.handleSupplierActivityExpire(mq.getActiveCode());
                            log.info("【活动过期消费者】供应商活动过期处理完成: {}", mq.getActiveCode());
                        } else {
                            // 处理分销商活动过期
                            productActiveService.handleDistributorActivityExpire(mq.getActiveCode());
                            log.info("【活动过期消费者】分销商活动过期处理完成: {}", mq.getActiveCode());
                        }
                    } catch (Exception e) {
                        log.error("【活动过期消费者】处理活动过期失败: 活动编码={}, 活动类型={}",
                            mq.getActiveCode(), mq.getType(), e);
                    }
                });

            } catch (Exception e) {
                log.error("【活动过期消费者】处理消息异常: {}", messageBody, e);
                // 可以考虑将失败的消息重新放入队列或记录到死信队列
            }
        });
    }
}
