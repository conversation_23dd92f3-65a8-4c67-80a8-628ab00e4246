package com.zsmall.activity.biz.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import com.zsmall.activity.biz.service.ProductActiveService;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 活动过期消息消费者
 * 监听活动过期消息，基于 RabbitMQ 延迟消息插件实现
 * 已迁移到Redis延时队列方案，此类已停用
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
//@Component  // 已注释，使用Redis方案替代
@Slf4j
@RequiredArgsConstructor
public class ProductActivityExpireConsumer {
    // @Resource  // 已注释，使用Redis方案替代
    // private ProductActiveService productActiveService;
    /**
     * 监听活动过期消息
     * 当延迟时间到达时，消息会被投递到这个队列
     *
     * @param message 消息对象
     * @param channel RabbitMQ通道
     * @param deliveryTag 消息投递标签
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE)
    public void handleActivityExpire(Message message,
                                   @Header(AmqpHeaders.CHANNEL) Channel channel,
                                   @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        String distributorActivityCode = null;
        try {
            // 获取消息内容（活动编码）
            String messageBody = new String(message.getBody());
            ActivityExpireMq mq = JSONUtil.toBean(messageBody, ActivityExpireMq.class);
            if (ObjectUtil.isNull(mq)){
              throw new RuntimeException("消息内容为空");
            }
            if (StrUtil.isEmpty(mq.getActiveCode())){
              throw new RuntimeException("活动编码为空");
            }
            if (ObjectUtil.isNull(mq.getType())){
                throw new RuntimeException("活动类型为空");
            }
            if (mq.getType() == 1){
                productActiveService.handleSupplierActivityExpire(mq.getActiveCode());
            }else {
                productActiveService.handleDistributorActivityExpire(mq.getActiveCode());
            }
            log.info("活动过期处理完成: {}", distributorActivityCode);
        } catch (Exception e) {
            log.error("处理活动过期消息失败: {}", distributorActivityCode, e);
        }finally {
            try {
                channel.basicAck(deliveryTag, false);
            } catch (IOException e) {
                log.error("确认消息失败", e);
            }
        }
    }

}
