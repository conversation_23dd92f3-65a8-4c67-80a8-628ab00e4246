package com.zsmall.activity.biz.config;

import com.zsmall.activity.biz.listener.ActivityExpireRedisConsumerListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 活动模块配置类
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Configuration
public class ActivityConfig {

    /**
     * 活动过期Redis消费者监听器
     * @return ActivityExpireRedisConsumerListener
     */
    @Bean
    public ActivityExpireRedisConsumerListener activityExpireRedisConsumerListener() {
        return new ActivityExpireRedisConsumerListener();
    }
}
