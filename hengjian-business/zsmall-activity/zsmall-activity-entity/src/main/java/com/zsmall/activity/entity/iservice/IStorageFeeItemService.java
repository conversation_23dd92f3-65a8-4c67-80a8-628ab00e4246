package com.zsmall.activity.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.StorageFeeItem;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeeItemBo;
import com.zsmall.activity.entity.domain.vo.storageFee.StorageFeeItemVo;
import com.zsmall.activity.entity.mapper.StorageFeeItemMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 仓储费子Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class IStorageFeeItemService extends ServiceImpl<StorageFeeItemMapper, StorageFeeItem> {

    private final StorageFeeItemMapper baseMapper;

    /**
     * 查询仓储费子
     */
    public StorageFeeItemVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询仓储费子列表
     */
    public TableDataInfo<StorageFeeItemVo> queryPageList(StorageFeeItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StorageFeeItem> lqw = buildQueryWrapper(bo);
        Page<StorageFeeItemVo> result = TenantHelper.ignore(() -> baseMapper.selectVoPage(pageQuery.build(), lqw));
        return TableDataInfo.build(result);
    }

    /**
     * 查询仓储费子列表
     */
    public List<StorageFeeItemVo> queryList(StorageFeeItemBo bo) {
        LambdaQueryWrapper<StorageFeeItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @InMethodLog("查询仓储费明细数据根据仓储费ID集合")
    public List<StorageFeeItemVo> queryExportList(List<String> storageFeeIdList) {
        LambdaQueryWrapper<StorageFeeItem> lqw = Wrappers.lambdaQuery();
        lqw.in(StorageFeeItem::getStorageFeeId, storageFeeIdList);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StorageFeeItem> buildQueryWrapper(StorageFeeItemBo bo) {
        LambdaQueryWrapper<StorageFeeItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStorageFeeId()), StorageFeeItem::getStorageFeeId, bo.getStorageFeeId());
        lqw.eq(StringUtils.isNotBlank(bo.getDetailId()), StorageFeeItem::getDetailId, bo.getDetailId());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), StorageFeeItem::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), StorageFeeItem::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(StringUtils.isNotBlank(bo.getSkuId()), StorageFeeItem::getSkuId, bo.getSkuId());
        lqw.eq(StringUtils.isNotBlank(bo.getSku()), StorageFeeItem::getSku, bo.getSku());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrencyCode()), StorageFeeItem::getCurrencyCode, bo.getCurrencyCode());
        lqw.eq(StringUtils.isNotBlank(bo.getSite()), StorageFeeItem::getSite, bo.getSite());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityId()), StorageFeeItem::getActivityId, bo.getActivityId());
        if(StringUtils.isNotBlank(bo.getFeeSettlementDateStart()) && StringUtils.isNotBlank(bo.getFeeSettlementDateEnd())){
            lqw.between(StorageFeeItem::getFeeSettlementDate, bo.getFeeSettlementDateStart(), bo.getFeeSettlementDateEnd());
        }
        return lqw;
    }

    /**
     * 新增仓储费子
     */
    public Boolean insertByBo(StorageFeeItemBo bo) {
        StorageFeeItem add = MapstructUtils.convert(bo, StorageFeeItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改仓储费子
     */
    public Boolean updateByBo(StorageFeeItemBo bo) {
        StorageFeeItem update = MapstructUtils.convert(bo, StorageFeeItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StorageFeeItem entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除仓储费子
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
