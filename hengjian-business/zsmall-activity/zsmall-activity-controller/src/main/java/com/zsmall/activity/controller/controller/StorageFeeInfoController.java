package com.zsmall.activity.controller.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import cn.hutool.core.collection.CollUtil;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.activity.biz.service.IStorageFeeService;
import com.zsmall.activity.biz.support.StorageFeeSupport;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeeInfoBo;
import com.zsmall.activity.entity.domain.bo.storageFee.StorageFeePayBo;
import com.zsmall.activity.entity.domain.vo.storageFee.StorageFeeInfoVo;
import com.zsmall.activity.entity.iservice.IStorageFeeInfoService;
import com.zsmall.common.domain.CurrencyAmountVO;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.order.entity.domain.vo.order.OrderCurrencyAmountVO;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.log.enums.BusinessType;

/**
 * 仓储费主
 *
 * <AUTHOR> Li
 * @date 2025-07-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/storageFee")
public class StorageFeeInfoController extends BaseController {

    private final IStorageFeeInfoService storageFeeInfoService;
    private final IStorageFeeService iStorageFeeService;
    private final StorageFeeSupport storageFeeSupport;

    /**
     * 查询仓储费主列表
     */
    @GetMapping("/list")
    public TableDataInfo<StorageFeeInfoVo> list(StorageFeeInfoBo bo, PageQuery pageQuery) {
        return storageFeeInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出仓储费主列表
     */
    @Log(title = "仓储费导出", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public R<Void> export(StorageFeeInfoBo bo, HttpServletResponse response) throws IOException {
        storageFeeInfoService.exportStorageFee(bo,response);
        return R.ok();
    }

    /**
     * 发送仓储费到分销商
     */
    @Log(title = "发送仓储费到分销商", businessType = BusinessType.UPDATE)
    @PostMapping("/sendStorageFee")
    public R<Void> sendStorageFee(@RequestBody List<String> storageFeeIdList) {
        if(CollUtil.isEmpty(storageFeeIdList)){
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        storageFeeInfoService.sendStorageFee(storageFeeIdList);
        return R.ok();
    }

    @Log(title = "获取仓储费金额")
    @GetMapping("/getStorageFee")
    public R<List<CurrencyAmountVO>> getStorageFee(@RequestParam("storageFeeIds") List<String> storageFeeIds) {
        if(CollUtil.isEmpty(storageFeeIds)){
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        return storageFeeInfoService.getStorageFee(storageFeeIds);
    }

    @Log(title = "仓储费支付")
    @PostMapping("/payStorageFee")
    public R<Void> payStorageFee(@RequestBody StorageFeePayBo storageFeePayBo) throws Exception {
        if(null == storageFeePayBo || CollUtil.isEmpty(storageFeePayBo.getStorageFeeIds())){
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        return iStorageFeeService.payStorageFee(storageFeePayBo);
    }

    /**
     * 生成仓储费明细
     * @return
     */
    @PostMapping("/generateStorageFee")
    public R<Void> storageFee() {
        storageFeeSupport.storageFee();
        return R.ok();
    }

    /**
     * 生成主仓储费
     * @param distributorActivityCode
     * @return
     */
    @PostMapping("/generateStorageFeeMain")
    public R<Void> storageFeeMain(@RequestParam("distributorActivityCode") String distributorActivityCode) {
        storageFeeSupport.storageFeeMain(distributorActivityCode);
        return R.ok();
    }

}
