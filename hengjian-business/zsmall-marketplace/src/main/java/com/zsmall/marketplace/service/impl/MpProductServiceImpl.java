package com.zsmall.marketplace.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.hengjian.system.service.ISysUserService;
import com.zsmall.activity.biz.support.ProductActiveSupper;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityDetails;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.product.*;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.extend.es.entity.EsProduct;
import com.zsmall.extend.es.esmapper.EsProductMapper;
import com.zsmall.marketplace.domain.bo.AddToFavoritesBo;
import com.zsmall.marketplace.domain.bo.MpProductSearchBo;
import com.zsmall.marketplace.domain.bo.mpWholesale.MpWholesaleProductBo;
import com.zsmall.marketplace.domain.bo.product.RecentlyProductBo;
import com.zsmall.marketplace.domain.vo.mpWholesale.*;
import com.zsmall.marketplace.domain.vo.product.*;
import com.zsmall.marketplace.service.MpProductService;
import com.zsmall.product.biz.service.IProductLabelService;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.domain.dto.stock.SpuStock;
import com.zsmall.product.entity.domain.dto.wholesale.WholesaleProductPageDTO;
import com.zsmall.product.entity.domain.dto.wholesale.WholesaleProductParamDTO;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;
import com.zsmall.product.entity.domain.vo.product.ProductSkuSimpleVo;
import com.zsmall.product.entity.domain.vo.wholesale.ProductSkuWholesaleVo;
import com.zsmall.product.entity.domain.vo.wholesale.ProductWholesaleTieredPriceVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.mapper.ProductMapper;
import com.zsmall.product.entity.mapper.TenantFavoritesMapper;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.IShippingReturnsService;
import com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import com.zsmall.warehouse.entity.iservice.IWarehouseAddressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.lucene.search.function.CombineFunction;
import org.elasticsearch.common.lucene.search.function.FunctionScoreQuery;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Cardinality;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.collapse.CollapseBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * Marketplace商品相关业务实现
 *
 * <AUTHOR>
 * @date 2023/7/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MpProductServiceImpl implements MpProductService {
    private final IProductService productService;
    private final IMemberLevelV2ServiceImpl iMemberLevelService;
//    private final IProductActivityService iProductActivityService;
//    private final IProductActivityStockService iProductActivityStockService;
//    private final IProductActivityStockLockService iProductActivityStockLockService;
//    private final IProductActivityBuyoutService iProductActivityBuyoutService;
    private final IProductCategoryService iProductCategoryService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductAttachmentService iProductAttachmentService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductLabelService iProductLabelService;
    private final ITenantFavoritesService iTenantFavoritesService;
    private final IWarehouseAddressService iWarehouseAddressService;
    private final IProductChannelControlService iProductChannelControlService;
    private final IShippingReturnsService iShippingReturnsService;
    private final ISysTenantService tenantService;
    private final IProductQuestionService iProductQuestionService;
    private final ISysTenantService iSysTenantService;
    private final ISysUserService iSysUserService;
    private final IProductWholesaleTieredPriceService iProductWholesaleTieredPriceService;
    private final IProductWholesaleDetailService iProductWholesaleDetailService;
    private final IProductReviewRecordService iProductReviewRecordService;
    private final IProductReviewChangeDetailService iProductReviewChangeDetailService;
    private final IProductSkuWholesalePriceService iProductSkuWholesalePriceService;
    private final TenantFavoritesMapper tenantFavoritesMapper;
    private final EsProductSupport esProductSupport;
//    private final ProductActivitySupport productActivitySupport;
    private final EsProductMapper esProductMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final BusinessParameterService businessParameterService;
    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final ProductMapper productMapper;
    private final SiteCountryCurrencyMapper siteCountryCurrencyMapper;
    private final ISysConfigService sysConfigService;
    private final ProductActiveSupper productActiveSupper;

    @Value("${distribution.sku.isVisibleForSku}")
    private boolean isVisibleForSku;

    /**
     * 初始化ElasticSearch商品数据
     */
    @Override
    public R<Void> initEsProduct() {
        LoginHelper.getLoginUser(TenantType.Manager);

        Boolean hasKey = RedisUtils.hasKey(RedisConstants.ZSMALL_PRODUCT_ELASTIC_SEARCH_INIT_KEY);
        if (hasKey) {
            return R.fail(ZSMallStatusCodeEnum.FREQUENT_OPERATIONS);
        }

        try {
            RedisUtils.setCacheObject(RedisConstants.ZSMALL_PRODUCT_ELASTIC_SEARCH_INIT_KEY, DateUtil.now());
            List<Product> productList = iProductService.queryNormalProductForES();
            if (CollUtil.isNotEmpty(productList)) {
                esProductSupport.productUpload(productList);
            }
        } finally {
            RedisUtils.deleteObject(RedisConstants.ZSMALL_PRODUCT_ELASTIC_SEARCH_INIT_KEY);
        }

        // threadPoolTaskExecutor.execute(() -> {
        //
        //     Page<Product> page = new Page<>(1, 50);
        //     Page<Product> productPage = iProductService.queryNormalProductForES();
        //     esProductSupport.productUpload(productPage.getRecords());
        //
        //     if (productPage.hasNext()) {
        //         long current = productPage.getCurrent();
        //         productPage = iProductService.queryNormalProductForES(productPage.setCurrent());
        //         esProductSupport.productUpload(productPage.getRecords());
        //     }
        //
        //     RedisUtils.deleteObject(RedisConstants.ZSMALL_PRODUCT_ELASTIC_SEARCH_INIT_KEY);
        // });
        return R.ok();
    }

    /**
     * 搜索最近商品
     *
     * @param bo
     */
    @Override
    public R<List<MpProductVo>> searchRecentlyProduct(RecentlyProductBo bo) {
        boolean login = StpUtil.isLogin();
        LoginUser loginUser = null;
        if (login) {
            loginUser = LoginHelper.getLoginUser();
        }

        List<MpProductVo> productVoList = new ArrayList<>();
        List<String> productSkuCodeList = bo.getProductSkuCodeList();
        if (CollUtil.isNotEmpty(productSkuCodeList)) {
            LambdaEsQueryWrapper<EsProduct> lqw = EsWrappers.lambdaQuery(EsProduct.class);
            lqw.size(10).from(0);
            SearchSourceBuilder builder = esProductMapper.getSearchSourceBuilder(lqw);
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

            boolQueryBuilder.filter(
                QueryBuilders.matchQuery("spu_state", 0)
            ).filter(
                QueryBuilders.matchQuery("sku_state", 0)
            ).filter(
                QueryBuilders.matchQuery("shelf_state", "OnShelf")
            ).filter(
                QueryBuilders.matchQuery("sku_shelf_state", "OnShelf")
            );

            for (String productSkuCode : productSkuCodeList) {
                boolQueryBuilder.should().add(
                    QueryBuilders.matchQuery("sku_code", productSkuCode)
                );
            }

            builder.collapse(new CollapseBuilder("spu_code.keyword"));
            builder.query(boolQueryBuilder.minimumShouldMatch(1));

            lqw.setSearchSourceBuilder(builder);
            SearchResponse search = esProductMapper.search(lqw);

            SearchHits hits = search.getHits();
            SearchHit[] hitArray = hits.getHits();

            if (ArrayUtil.isNotEmpty(hitArray)) {
                productVoList.addAll(convertToMpProductVo(loginUser, List.of(hitArray)));
            }
        }

        return R.ok(productVoList);
    }

    /**
     * 首页查询商品
     *
     * @param bo
     * @param
     */
    @Override
    public R<MpProductListVo> searchProductPage(MpProductSearchBo bo) throws Exception {
        if (StrUtil.isEmpty(bo.getSite())) {
            return R.fail("站点不能为空");
        }
        boolean login = StpUtil.isLogin();
        LoginUser loginUser = null;
        if (login) {
            loginUser = LoginHelper.getLoginUser();
        }

        String queryValue = bo.getQueryValue();
        if (StrUtil.isNotEmpty(queryValue)){
            queryValue=StrUtil.trim(queryValue);
        }
        List<Long> categoryIdList = bo.getCategoryIdList();
        String fixedLabel = bo.getFixedLabel();
        String activityType = bo.getActivityType();
        List<Long> labelIdList = bo.getLabelIdList();
        BigDecimal minPrice = bo.getMinPrice();
        BigDecimal maxPrice = bo.getMaxPrice();
        Integer shippingDayMin = bo.getShippingDayMin();
        Integer shippingDayMax = bo.getShippingDayMax();
        List<String> channelList = bo.getChannelList();
        List<Integer> supportedLogisticsList = bo.getSupportedLogistics();
        String sortType = bo.getSortType();

        Integer pageNum = bo.getPageNum();
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }

        Integer pageSize = bo.getPageSize();
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        Future<List<MpProductLabelVo>> labelListFuture = ThreadUtil.execAsync(() -> {
            List<ProductLabel> labelList = iProductLabelService.queryAllOrderBySort();
            List<MpProductLabelVo> labelVoList = BeanUtil.copyToList(labelList, MpProductLabelVo.class);
            return labelVoList;
        });

        LambdaEsQueryWrapper<EsProduct> lqw = EsWrappers.lambdaQuery(EsProduct.class);
        lqw.size(pageSize).from(Math.abs(pageNum - 1) * pageSize);

        SearchSourceBuilder builder = esProductMapper.getSearchSourceBuilder(lqw);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        builder.collapse(new CollapseBuilder("spu_code.keyword"));
        builder.query(boolQueryBuilder.filter(
            QueryBuilders.matchQuery("spu_state", 0).operator(Operator.AND)
        ).filter(
            QueryBuilders.matchQuery("sku_state", 0).operator(Operator.AND)
        ).filter(
            QueryBuilders.matchQuery("shelf_state", "OnShelf").operator(Operator.AND)
        ).filter(
            QueryBuilders.matchQuery("sku_shelf_state", "OnShelf").operator(Operator.AND)
        ));

        if (StrUtil.isNotBlank(fixedLabel)) {
            ProductLabel productLabel = iProductLabelService.queryByLabelName(fixedLabel);
            if (productLabel != null) {
                if (labelIdList == null) {
                    labelIdList = new ArrayList<>();
                }

                if (!labelIdList.contains(productLabel.getId())) {
                    labelIdList.add(productLabel.getId());
                }
            }
        }

        // 关键词查询
        if (StringUtils.isNotBlank(queryValue)) {
            String finalQueryValue = queryValue;
            List<ProductSku> list = TenantHelper.ignore(()->iProductSkuService.list(new LambdaQueryWrapper<>(ProductSku.class).eq(ProductSku::getSku, finalQueryValue).eq(ProductSku::getDelFlag,0)));


            String supCode = "^S[A-Z0-9]{6}$";
            if (StrUtil.length(queryValue) == 8 && ReUtil.isMatch(supCode, queryValue)) {
                boolQueryBuilder.must(QueryBuilders.matchQuery("supplier_id", queryValue));
            } else {

                if(CollUtil.isNotEmpty(list)){
                    boolQueryBuilder.must(QueryBuilders.termQuery("sku.keyword", queryValue));
                }else{
                    boolQueryBuilder.must(QueryBuilders.multiMatchQuery(queryValue)
                                                       .field("category1_name", 10)
                                                       .field("category2_name", 10)
                                                       .field("category3_name", 10)
                                                       .field("product_name", 100)
                                                       .field("specification", 10)
                                                       .field("attributes", 10)
                                                       .field("features", 100)
                                                       .field("sku_code", 100)
                                                       .field("spu_code", 100)
                                                       .field("sku", 100)
                    );
                }
            }
        }

        // 分类查询
        if (CollUtil.isNotEmpty(categoryIdList)) {
            for (Long categoryId : categoryIdList) {
                boolQueryBuilder.filter(QueryBuilders.multiMatchQuery(categoryId, "category1_id", "category2_id", "category3_id"));
            }
        }

        // 标签查询：与关系
        if (CollUtil.isNotEmpty(labelIdList)) {
            Long[] labelIds = ArrayUtil.toArray(labelIdList, Long.class);
            for (Long labelId : labelIds) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("label_group", labelId));
            }
        }

        if (StrUtil.isNotBlank(activityType)) {
            boolQueryBuilder.filter(QueryBuilders.matchQuery("activity_types", activityType));
        }

        // 物流类型
        if (CollUtil.isNotEmpty(supportedLogisticsList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("supported_logistics", supportedLogisticsList));
        }

        // 可售渠道
        if (CollUtil.isNotEmpty(channelList)) {
            List<Integer> convertChannelId = ChannelTypeEnum.convertChannelId(channelList);
            for (Integer channelId : convertChannelId) {
                boolQueryBuilder.mustNot(QueryBuilders.matchQuery("forbidden_channel", channelId));
            }
        }

        // 价格筛选
        if (minPrice != null && maxPrice != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("main_price").gte(minPrice).lte(maxPrice));
        }

        // 到货日期筛选
        if (shippingDayMin != null && shippingDayMax != null) {
            boolQueryBuilder.filter(QueryBuilders.matchQuery("shipping_day_min", shippingDayMin));
            boolQueryBuilder.filter(QueryBuilders.matchQuery("shipping_day_max", shippingDayMax));
        }

        if (loginUser != null && ObjectUtil.equals(loginUser.getTenantTypeEnum(), TenantType.Distributor)) {
            boolQueryBuilder.should(QueryBuilders.matchQuery("allow_tenant_id", "ALL"));
            boolQueryBuilder.should(QueryBuilders.matchQuery("allow_tenant_id", loginUser.getTenantId()));
            boolQueryBuilder.minimumShouldMatch(1);
        } else {
            boolQueryBuilder.should(QueryBuilders.matchQuery("allow_tenant_id", "ALL"));
            boolQueryBuilder.minimumShouldMatch(1);
        }
        // 租户id不是 SZH6F1A 或者 D4ZQGHV 那么看不到 ZJHJ开头的商品

//        boolQueryBuilder.mustNot(QueryBuilders.wildcardQuery("sku.keyword", "ZJHJ*"));
        // 只有香港恒健能够看到浙江恒健的以ZJHJ开头的商品
        if (loginUser != null && (!ObjectUtil.equals(loginUser.getTenantId(), "S0BJHUA") && !ObjectUtil.equals(loginUser.getTenantId(), "D4ZQGHV"))) {
            // boolQueryBuilder 排除ZJHJ开头的sku
            boolQueryBuilder.mustNot(QueryBuilders.wildcardQuery("sku.keyword", "ZJHJ*"));
        }
        if(ObjectUtil.isEmpty(loginUser)){
            boolQueryBuilder.mustNot(QueryBuilders.wildcardQuery("sku.keyword", "ZJHJ*"));
        }

        FunctionScoreQueryBuilder.FilterFunctionBuilder[] filterFunctionBuilders = new FunctionScoreQueryBuilder.FilterFunctionBuilder[2];
        filterFunctionBuilders[0] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(ScoreFunctionBuilders.scriptFunction("doc['main_price'].value"));
//        filterFunctionBuilders[1] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(ScoreFunctionBuilders.scriptFunction("doc['stock_total'].value == 0 ? 1000000 : 0"));
        filterFunctionBuilders[1] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(
            ScoreFunctionBuilders.scriptFunction(
                "if (doc['stock_total'].size() == 0) { " +
                    "  return 0; " +
                    "} else { " +
                    "  return doc['stock_total'].value == 0 ? 1000000 : 0; " +
                    "}"
            )
        );
        FunctionScoreQueryBuilder functionScoreQuery = QueryBuilders.functionScoreQuery(boolQueryBuilder, filterFunctionBuilders);
        functionScoreQuery.scoreMode(FunctionScoreQuery.ScoreMode.SUM);
        functionScoreQuery.boostMode(CombineFunction.REPLACE);

        // 排序
        if (StringUtils.equals("PriceAsc", sortType)) {
            builder.sort("main_price", SortOrder.ASC);
        } else if (StringUtils.equals("PriceDesc", sortType)) {
            builder.sort("main_price", SortOrder.DESC);
        } else if (StringUtils.equals("StockAsc", sortType)) {
            builder.sort("stock_total", SortOrder.ASC);
        } else if (StringUtils.equals("StockDesc", sortType)) {
            builder.sort("stock_total", SortOrder.DESC);
        } else if (StringUtils.equals("Newest", sortType)) {
            builder.sort("create_time", SortOrder.DESC);
        } else {
            builder.sort("_score", SortOrder.ASC);
        }

        TermsAggregationBuilder tBuilder = AggregationBuilders.terms("category1").field("category1_id").size(11)
                                                              .subAggregation(
                                                                  AggregationBuilders.terms("category2")
                                                                                     .field("category2_id").size(11)
                                                                                     .subAggregation(
                                                                                         AggregationBuilders.terms("category3")
                                                                                                            .field("category3_id")
                                                                                                            .size(11)
                                                                                     )
                                                              );
        CardinalityAggregationBuilder cBuilder = AggregationBuilders.cardinality("totalCount")
                                                                    .field("spu_code.keyword");
        builder.aggregation(tBuilder);
        builder.aggregation(cBuilder);
        builder.query(functionScoreQuery);

        lqw.setSearchSourceBuilder(builder);
        SearchResponse search = esProductMapper.search(lqw);

        SearchHits hits = search.getHits();
        SearchHit[] hitArray = hits.getHits();
        List<MpProductVo> productVoList = new ArrayList<>();
        if (ArrayUtil.isNotEmpty(hitArray)) {
            productVoList.addAll(convertToMpProductVo(loginUser, List.of(hitArray)));
        }
        Set<String> itemNoSet = productVoList.stream()
                                           .map(MpProductVo::getProductSkuCode)
                                           .collect(Collectors.toSet());
        Map<String, ProductSkuPrice> productSkuSitePriceMapByCode = iProductSkuPriceService.getProductSkuSitePriceMapByCode(itemNoSet);
        Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode = iProductSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(itemNoSet, LoginHelper.getTenantId());
        //处理价格
        productVoList.forEach(s->{
            ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(s.getProductSkuCode() + "-" + bo.getSite());
            if (ObjectUtil.isNotNull(productSkuPrice)){
                s.setMainPrice(productSkuPrice.getOriginalPickUpPrice());
            }else{
                s.setMainPrice(null);
            }
            RuleLevelProductPrice ruleLevelProductPrice = ruleLevelProductPriceSitePriceMapByCode.get(s.getProductSkuCode() + "-" + bo.getSite());
            if (ObjectUtil.isNotNull(ruleLevelProductPrice)){
                s.setMemberPrice(ruleLevelProductPrice.getOriginalPickUpPrice());
            }else{
                s.setMemberPrice(null);
            }
        });

        Cardinality totalCount = search.getAggregations().get("totalCount");
        Long totalElement = totalCount.getValue();

        Aggregation category1 = search.getAggregations().get("category1");
        List<MpProductCategoryVo> productCategoryVoList = new ArrayList<>();
        if (category1 != null) {
            productCategoryVoList.addAll(recursionAggsCategory(ServletUtils.getHeaderLanguage(), category1, "category", 1));
        }

        MpProductListVo vo = new MpProductListVo();
        vo.setCategoryList(productCategoryVoList);
        vo.setResults(productVoList);
        vo.setLabelList(labelListFuture.get());
        vo.setTotal(totalElement);
        vo.setTotalPage(PageUtil.totalPage(totalElement, pageSize));
        return R.ok(vo);
    }

    /**
     * 查询批发商品
     *
     * @param bo
     * @param
     */
    @Override
    public MpWholesaleProductPageVo searchWholesaleProductPage(MpWholesaleProductBo bo) {
        boolean login = StpUtil.isLogin();
        LoginUser loginUser = null;
        if (login) {
            loginUser = LoginHelper.getLoginUser();
        }

        Integer pageNo = bo.getPageNum();
        Integer pageSize = bo.getPageSize();
        String sortType = bo.getSortType();
        String queryValue = bo.getQueryValue();

        BigDecimal minPrice = bo.getMinPrice();
        BigDecimal maxPrice = bo.getMaxPrice();

        WholesaleProductParamDTO param = new WholesaleProductParamDTO();
        if (ObjectUtil.isAllNotEmpty(minPrice, maxPrice)) {
            param.setMinPrice(minPrice);
            param.setMaxPrice(maxPrice);
        }
        if (StrUtil.isNotBlank(queryValue)) {
            param.setQueryValue(queryValue);
        }

        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        // 查询
        OrderItem orderItem;
        // 排序
        if (StrUtil.equals("PriceAsc", sortType)) {
            orderItem = OrderItem.asc("mainPrice");
        } else if (StrUtil.equals("PriceDesc", sortType)) {
            orderItem = OrderItem.desc("mainPrice");
        } else if (StrUtil.equals("StockAsc", sortType)) {
            orderItem = OrderItem.asc("stock");
        } else if (StrUtil.equals("StockDesc", sortType)) {
            orderItem = OrderItem.desc("stock");
        } else if (StrUtil.equals("Newest", sortType)) {
            orderItem = OrderItem.desc("createTime");
        } else {
            orderItem = OrderItem.asc("mainPrice");
        }

        Page<WholesaleProductPageDTO> queryPage = new Page<>(pageNo, pageSize);
        queryPage.addOrder(orderItem);

        IPage<WholesaleProductPageDTO> wholesaleProductPage = iProductSkuService.getWholesaleProductPage(param, queryPage);
        List<WholesaleProductPageDTO> records = wholesaleProductPage.getRecords();
        List<WholesaleProductPageVo> results = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
//            List<String> favoritesProductCodes = new ArrayList<>();
//            if (loginUser != null) {
//                // 收藏的商品编号
//                favoritesProductCodes = iTenantFavoritesService.queryAllProductCode();
//            }

            for (WholesaleProductPageDTO record : records) {
                WholesaleProductPageVo pageVo = new WholesaleProductPageVo();
                String name = record.getName();
                Long productId = record.getProductId();
                String productCode = record.getProductCode();
                String productSkuCode = record.getProductSkuCode();
                String mainPrice = record.getMainPrice();
              //  Integer totalStock = record.getStock();
                ProductSkuAttachmentVo firstImage = iProductSkuAttachmentService.queryFirstImageByProductId(productId);

                pageVo.setProductCode(productCode);
                pageVo.setProductSkuCode(productSkuCode);
                pageVo.setMinPrice(ObjectUtil.isEmpty(mainPrice) ? 0.0 : Double.valueOf(mainPrice));
                pageVo.setName(name);
                SpuStock spuStock = TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper().getSpuStock(productCode));
                pageVo.setPickUpStockTotal(spuStock != null ? spuStock.getPickUpStockTotal() : 0);
                pageVo.setDropShippingStockTotal(spuStock != null ? spuStock.getDropShippingStockTotal() : 0);
          //      pageVo.setTotalStock(totalStock);
                pageVo.setImageUrl(firstImage != null ? firstImage.getAttachmentShowUrl() : null);
                pageVo.setCollected(RedisUtils.hasKey(GlobalConstants.FAVORITES_SPU + LoginHelper.getTenantId() + productCode));
                results.add(pageVo);
            }
        }

        MpWholesaleProductPageVo vo = new MpWholesaleProductPageVo();
        vo.setRows(results);
        vo.setTotal(wholesaleProductPage.getTotal());
        vo.setWholesaleOrderRegulation(businessParameterService.getValueFromString(BusinessParameterType.WHOLESALE_ORDER_REGULATION));
        return vo;
    }

    /**
     * 查询分类树
     */
    @Override
    public R<List<MpProductCategoryVo>> searchProductCategoryTree() {
        LambdaEsQueryWrapper<EsProduct> lqw = EsWrappers.lambdaQuery(EsProduct.class);
        lqw.size(0).from(0);

        SearchSourceBuilder builder = esProductMapper.getSearchSourceBuilder(lqw);
        builder.collapse(new CollapseBuilder("spu_code.keyword"));
        builder.query(QueryBuilders.boolQuery().filter(
            QueryBuilders.matchQuery("spu_state", 0).operator(Operator.AND)
        ).filter(
            QueryBuilders.matchQuery("sku_state", 0).operator(Operator.AND)
        ).filter(
            QueryBuilders.matchQuery("shelf_state", "OnShelf").operator(Operator.AND)
        ).filter(
            QueryBuilders.matchQuery("sku_shelf_state", "OnShelf").operator(Operator.AND)
        ));

        TermsAggregationBuilder tBuilder = AggregationBuilders.terms("category1").field("category1_id").size(11)
                                                              .subAggregation(
                                                                  AggregationBuilders.terms("category2")
                                                                                     .field("category2_id").size(11)
                                                                                     .subAggregation(
                                                                                         AggregationBuilders.terms("category3")
                                                                                                            .field("category3_id")
                                                                                                            .size(11)
                                                                                     )
                                                              );
        CardinalityAggregationBuilder cBuilder = AggregationBuilders.cardinality("totalCount")
                                                                    .field("spu_code.keyword");

        builder.aggregation(tBuilder);
        builder.aggregation(cBuilder);

        lqw.setSearchSourceBuilder(builder);
        log.info("DSL语句 => {}", builder.toString());
        SearchResponse search = esProductMapper.search(lqw);
        Aggregation category1 = search.getAggregations().get("category1");
        List<MpProductCategoryVo> voList = new ArrayList<>();
        if (category1 != null) {
            voList = recursionAggsCategory(ServletUtils.getHeaderLanguage(), category1, "category", 1);
        }
        return R.ok(voList);
    }

    /**
     * 获取商品详情
     *
     * @param productCode
     */
    @Override
    public R<MpProductDetailVo> getProductDetail(String productCode,String site) throws Exception {
        if (StrUtil.isEmpty(site)) {
            return R.fail("站点不能为空");
        }
        // 判断商品是否是ZJHJ加密商品
        List<ProductSku> productSkus = TenantHelper.ignore(() -> iProductSkuService.list(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductCode, productCode)));
        for (ProductSku skus : productSkus) {
            String sku = skus.getSku();
            boolean matches = sku.matches("^ZJHJ.*");
            if(matches){
                // 判断当前用户的租户id
                LoginUser loginUser = LoginHelper.getLoginUser();
                // 游客
                if(ObjectUtil.isEmpty(loginUser)){
                    throw new Exception("很抱歉，暂无查看权限");
                }
                if(!ObjectUtil.equals(loginUser.getTenantId(), "S0BJHUA") && !ObjectUtil.equals(loginUser.getTenantId(), "D4ZQGHV")){
                    throw new Exception("很抱歉，暂无查看权限");
                }
            }
        }


        boolean login = StpUtil.isLogin();
        LoginUser loginUser = null;
        if (login) {
            loginUser = LoginHelper.getLoginUser();
        }

        LambdaQueryWrapper<Product> lqws = Wrappers.lambdaQuery();
        lqws.eq(Product::getProductCode, productCode);
        Product product= TenantHelper.ignore(()->productMapper.selectOne(lqws));
      //   Product product = iProductService.queryByProductCodeNotTenant(productCode);
        if (product == null || ObjUtil.notEqual(product.getShelfState(), ShelfStateEnum.OnShelf)) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }

        Long productId = product.getId();
        SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

        ProductSkuAttachmentVo firstImageVo = iProductSkuAttachmentService.queryFirstImageByProductId(productId);
        String productNotice = iShippingReturnsService.getDefaultShippingReturnsContentsBySupplierId(product.getTenantId());

        List<ProductAttachment> productAttachments = iProductAttachmentService.queryByProductIdAndType(productId, AttachmentTypeEnum.File);
        List<ProductAttribute> productAttributes = iProductAttributeService.queryByProductId(productId);

        List<ProductSku> productSkuList = iProductSkuService.getAcceptedAndOnShelfProductSkuListByProductId(productId);
        if (CollUtil.isEmpty(productSkuList)) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_DELIST);
        }

        List<ProductCategory> productCategories = iProductCategoryService.queryCategoryChainById(product.getBelongCategoryId());
        List<MpProductDetailCategoryVo> categoryVoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(productCategories)) {
            categoryVoList.addAll(BeanUtil.copyToList(CollUtil.reverse(productCategories), MpProductDetailCategoryVo.class));
        }

        MpProductDetailVo vo = new MpProductDetailVo();

        vo.setDeliveryTime(product.getDeliveryTime());
        vo.setDeliverGoodsTime(product.getDeliverGoodsTime());
        vo.setName(product.getName());
        vo.setProductCode(product.getProductCode());
        vo.setFirstImageShowUrl(firstImageVo.getAttachmentShowUrl());
        vo.setShelfState(product.getShelfState().name());
        vo.setProductNotice(productNotice);
        vo.setDescription(product.getDescription());
        vo.setSupportedLogistics(supportedLogistics.name());
        vo.setForbiddenChannels(product.getForbiddenChannel());
        vo.setCategoryList(categoryVoList);
        vo.setFavorites(iTenantFavoritesService.inFavorites(product.getProductCode()));

        // 商品附件
        if (CollUtil.isNotEmpty(productAttachments)) {
            ProductAttachment productAttachment = productAttachments.get(0);
            vo.setOtherAttachment(BeanUtil.toBean(productAttachment, MpAttachmentVo.class));
        }

        // 商品属性
        for (ProductAttribute productAttribute : productAttributes) {
            String attributeName = productAttribute.getAttributeName();
            String attributeValue = productAttribute.getAttributeValue();

            if (StrUtil.isBlank(attributeValue)) {
                continue;
            }

            MpProductAttributeVo attributeVo = new MpProductAttributeVo(attributeName, attributeValue, productAttribute.getAttributeValues());
            AttributeTypeEnum attributeType = productAttribute.getAttributeType();
            ReflectUtil.invoke(vo, "add" + attributeType.name(), attributeVo);
        }

        // 查询可用活动
//        ProductActivityTypeEnum[] values = ProductActivityTypeEnum.values();
//        for (ProductActivityTypeEnum value : values) {
//            List<MpProductActivitySelectVo> mpProductActivitySelectVos = iProductActivityService.queryMpProductActivitySelect(productCode, null, value);
//          ReflectUtil.setFieldValue(vo, value.name(), mpProductActivitySelectVos);
//        }
        Map<String, Map<String, List<SupplierProductActivityDetails>>> supplierAvailableActivesMapBySpu = productActiveSupper.getSupplierAvailableActivesMapBySpu(productCode, site);
        List<MpProductSkuDetailVo> skuList = new ArrayList<>();
        List<BigDecimal> priceList = new ArrayList<>();
        String ruleCustomizerTenantId = null;
        List<Long> productSkuIdList = new ArrayList<>();
        for (ProductSku productSku : productSkuList) {
            String productSkuCode = productSku.getProductSkuCode();
            if(StringUtils.isEmpty(ruleCustomizerTenantId)){
                ruleCustomizerTenantId = productSku.getTenantId();
            }
            Long productSkuId = productSku.getId();
            productSkuIdList.add(productSku.getId());
            ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuId(productSkuId);

            MpProductSkuDetailVo skuDetailVo = BeanUtil.toBean(productSku, MpProductSkuDetailVo.class);
            skuDetailVo.setSpecValNames(StrUtil.split(skuDetailVo.getSpecValName(), "/"));
            if (ObjectUtil.isNotNull(productSkuDetail)){
                skuDetailVo.setTransportMethod(productSkuDetail.getTransportMethod());
            }

            Map<String, ProductSkuPrice> productSkuSitePriceMapByCode = iProductSkuPriceService.getProductSkuSitePriceMapByCode(Set.of(productSkuCode));
            ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(productSkuCode + "-" + site);
            if (ObjectUtil.isNotNull(productSkuPrice)){
            skuDetailVo.setDropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
            skuDetailVo.setPickUpPrice(productSkuPrice.getPlatformPickUpPrice());
            skuDetailVo.setMsrp(productSkuPrice.getMsrp());
            }
            //处理会员价
            Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode = iProductSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(Set.of(productSkuCode), LoginHelper.getTenantId());
            RuleLevelProductPrice ruleLevelProductPrice = ruleLevelProductPriceSitePriceMapByCode.get(productSkuCode + "-" + site);
            if (ObjectUtil.isNotNull(ruleLevelProductPrice)){
                TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
                if(TenantType.Distributor.equals(tenantTypeEnum)){
                    skuDetailVo.setMemberPrice(ruleLevelProductPrice.getPlatformPickUpPrice());
                    skuDetailVo.setDropMemberPrice(ruleLevelProductPrice.getPlatformDropShippingPrice());
                }else {
                    skuDetailVo.setMemberPrice(ruleLevelProductPrice.getPlatformPickUpPrice());
                    skuDetailVo.setDropMemberPrice(ruleLevelProductPrice.getPlatformDropShippingPrice());
                }
            }else {
                skuDetailVo.setMemberPrice(null);
                skuDetailVo.setDropMemberPrice(null);
            }
            skuDetailVo.setProductSkuId(productSku.getId());
            skuDetailVo.setIsTenantFavorite(false);
            if (Objects.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)){
                LambdaQueryWrapper<TenantFavorites> t = new LambdaQueryWrapper<>();
                t.eq(TenantFavorites::getProductSkuCode,skuDetailVo.getProductSkuCode());
                t.groupBy(TenantFavorites::getProductSkuCode);
                TenantFavorites tenantFavorites = iTenantFavoritesService.getBaseMapper().selectOne(t);
                if (ObjectUtil.isNull(tenantFavorites)){
                    skuDetailVo.setIsTenantFavorite(false);
                } else {
                    skuDetailVo.setIsTenantFavorite(true);
                }
            }
            //设置分仓库存数据
            List<HashMap<String,Object>> stockMapList= TenantHelper.ignore(()->iProductSkuPriceService.getStockWithWarehouseSystemCodeBySite(productSku.getProductSkuCode(),productSku.getTenantId(),site));
            skuDetailVo.setSkuStockList(stockMapList);
            if (ObjectUtil.isNotNull(productSkuDetail)){
                BigDecimal length = productSkuDetail.getLength();
                BigDecimal width = productSkuDetail.getWidth();
                BigDecimal height = productSkuDetail.getHeight();
                LengthUnitEnum lengthUnit = productSkuDetail.getLengthUnit();

                BigDecimal weight = productSkuDetail.getWeight();
                WeightUnitEnum weightUnit = productSkuDetail.getWeightUnit();

                BigDecimal packLength = productSkuDetail.getPackLength();
                BigDecimal packWidth = productSkuDetail.getPackWidth();
                BigDecimal packHeight = productSkuDetail.getPackHeight();
                LengthUnitEnum packLengthUnit = productSkuDetail.getPackLengthUnit();

                BigDecimal packWeight = productSkuDetail.getPackWeight();
                WeightUnitEnum packWeightUnit = productSkuDetail.getPackWeightUnit();



            StringBuilder sizeBuilder = new StringBuilder().append(length).append(" x ").append(width).append(" x ")
                                                           .append(height).append(" ").append(lengthUnit.name());
            StringBuilder packSizeBuilder = new StringBuilder().append(packLength).append(" x ").append(packWidth)
                                                               .append(" x ").append(packHeight).append(" ")
                                                               .append(packLengthUnit.name());
            StringBuilder weightBuilder = new StringBuilder().append(weight).append(" ").append(weightUnit.name());
            StringBuilder packWeightBuilder = new StringBuilder().append(packWeight).append(" ")
                                                                 .append(packWeightUnit.name());

            skuDetailVo.setSize(sizeBuilder.toString());
            skuDetailVo.setWeight(weightBuilder.toString());
            skuDetailVo.setPackSize(packSizeBuilder.toString());
            skuDetailVo.setPackWeight(packWeightBuilder.toString());
            }
            // SKU附件
            List<ProductSkuAttachment> imageAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Image);
            List<ProductSkuAttachment> videoAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Video);

            if (CollUtil.isNotEmpty(imageAttachmentList)) {
                skuDetailVo.setImageShowUrl(imageAttachmentList.get(0).getAttachmentShowUrl());
            }

            skuDetailVo.setAttachmentList(BeanUtil.copyToList(imageAttachmentList, MpAttachmentVo.class));
            skuDetailVo.setVideoAttachmentList(BeanUtil.copyToList(videoAttachmentList, MpAttachmentVo.class));

//            if (SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)) {
//                priceList.add(productSkuPrice.getPlatformDropShippingPrice());
//            } else {
//                priceList.add(productSkuPrice.getPlatformPickUpPrice());
//            }

            List<WarehouseAddress> warehouseAddresses = iWarehouseAddressService.queryByProductSkuCode(productSkuCode);
            Set<String> shippingFromSet = warehouseAddresses.stream().map(WarehouseAddress::getCountry)
                                                            .collect(Collectors.toSet());
            skuDetailVo.setShippingFrom(CollUtil.join(shippingFromSet, ", "));
            skuDetailVo.setProcessingTime(iProductSkuService.queryDealEffectiveness(productSkuCode));

            // 渠道管控
            boolean allowSale = true;
            if (loginUser != null) {
                String tenantId = loginUser.getTenantId();
                ProductChannelControl allChannelControl =
                    iProductChannelControlService.queryAllChannelControl(productSkuCode);
                if (allChannelControl != null) {
                    JSONArray allowCodes = allChannelControl.getAllowTenantId();
                    if (CollUtil.isNotEmpty(allowCodes)) {
                        allowSale = allowCodes.contains(tenantId);
                    }
                }
            }

            ProductSku sku = TenantHelper.ignore(() -> iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, productSku.getProductSkuCode())
                                                                                                                     .last("limit 1")));
            // sku
            if (isVisibleForSku) {
                skuDetailVo.setSku(sku.getSku());
            } else {
                // 后期根据分销商 供应商来进行数据过滤 目前先不做
            }
            skuDetailVo.setAllowSale(allowSale);
//            int totalStockAvailable = stockMapList.stream()
//                    .filter(stockMap -> stockMap.containsKey("stockAvailable"))
//                    .mapToInt(stockMap -> (Integer) stockMap.get("stockAvailable"))
//                    .sum();
            SkuStock skuStock =TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper().getSkuStockBySite(productSkuCode,site));
            skuDetailVo.setPickUpStockTotal(skuStock != null ? skuStock.getPickUpStockTotal() : 0);
            skuDetailVo.setDropShippingStockTotal(skuStock != null ? skuStock.getDropShippingStockTotal() : 0);
            //处理活动
            Map<String, BigDecimal> prices = productActiveSupper.getSkuLowestPrices(supplierAvailableActivesMapBySpu, productSkuCode);
            skuDetailVo.setDropShippingActivePrice(prices.get("DropShippingOnly"));
            skuDetailVo.setPickUpActivePrice( prices.get("PickUpOnly"));
            skuDetailVo.setProductActivitieMap(supplierAvailableActivesMapBySpu.get(productSkuCode));
            skuList.add(skuDetailVo);
        }
        // 供应商这个逻辑不适用
//        String tenantId = LoginHelper.getTenantId();
//        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
//        Long levelId;
//        if(TenantType.Distributor.equals(tenantTypeEnum)){
//            if(StringUtils.isNotEmpty(ruleCustomizerTenantId)){
//                LambdaQueryWrapper<MemberRuleRelation> lqw = new LambdaQueryWrapper<>();
//                lqw.eq(MemberRuleRelation::getRuleCustomizerTenantId,ruleCustomizerTenantId)
//                   .eq(MemberRuleRelation::getRuleFollowerTenantId,tenantId)
//                   .eq(MemberRuleRelation::getDelFlag,0);
//                MemberRuleRelation memberRuleRelation = memberRuleRelationService.getOne(lqw);
//                if(null != memberRuleRelation){
//                    levelId = memberRuleRelation.getLevelId();
//                } else {
//                    levelId = null;
//                }
//            } else {
//                levelId = null;
//            }
//            if(null != levelId){
//                MemberLevel level = TenantHelper.ignore(()->iMemberLevelService.getById(levelId));
//                if(level.getStatus().equals(0)){
//                    LambdaQueryWrapper<RuleLevelProductPrice> levelProductPriceLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                    levelProductPriceLambdaQueryWrapper.eq(RuleLevelProductPrice::getRuleCustomizerTenantId,ruleCustomizerTenantId)
//                                                       .eq(RuleLevelProductPrice::getLevelId,levelId)
//                                                       .in(RuleLevelProductPrice::getProductSkuId,productSkuIdList)
//                                                       .eq(RuleLevelProductPrice::getDelFlag,0);
//                    List<RuleLevelProductPrice> ruleLevelProductPriceList = TenantHelper.ignore(() -> ruleLevelProductPriceService.list(levelProductPriceLambdaQueryWrapper));
//                    if(CollectionUtil.isNotEmpty(ruleLevelProductPriceList) && CollectionUtil.isNotEmpty(skuList)){
//                        loop:for(MpProductSkuDetailVo mpProductSkuDetailVo : skuList){
//                            for(RuleLevelProductPrice ruleLevelProductPrice : ruleLevelProductPriceList){
//                                if(null != mpProductSkuDetailVo.getProductSkuId() && null != ruleLevelProductPrice.getProductSkuId() && mpProductSkuDetailVo.getProductSkuId().equals(ruleLevelProductPrice.getProductSkuId())){
//                                    mpProductSkuDetailVo.setMemberPrice(ruleLevelProductPrice.getPlatformPickUpPrice());
//                                    mpProductSkuDetailVo.setDropMemberPrice(ruleLevelProductPrice.getPlatformDropShippingPrice());
//                                    continue loop;
//                                }
//                            }
//                        }
//                    }
//                }
//
//            }
//        }
//        if(TenantType.Supplier.equals(tenantTypeEnum)){
//            List<MemberLevel> memberLevels = iMemberLevelService.list(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDelFlag, 0)
//                                                                                                   .eq(MemberLevel::getStatus, 0));
//            for (MemberLevel memberLevel : memberLevels) {
//                if(null != memberLevel.getId()){
//                    MemberLevel level = iMemberLevelService.getById(memberLevel.getId());
//                    if(level.getStatus().equals(0)){
//                        LambdaQueryWrapper<RuleLevelProductPrice> levelProductPriceLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                        levelProductPriceLambdaQueryWrapper.eq(RuleLevelProductPrice::getRuleCustomizerTenantId,ruleCustomizerTenantId)
//                                                           .eq(RuleLevelProductPrice::getLevelId,memberLevel.getId())
//                                                           .in(RuleLevelProductPrice::getProductSkuId,productSkuIdList)
//                                                           .eq(RuleLevelProductPrice::getDelFlag,0);
//                        List<RuleLevelProductPrice> ruleLevelProductPriceList = TenantHelper.ignore(() -> ruleLevelProductPriceService.list(levelProductPriceLambdaQueryWrapper));
//                        if(CollectionUtil.isNotEmpty(ruleLevelProductPriceList) && CollectionUtil.isNotEmpty(skuList)){
//                            loop:for(MpProductSkuDetailVo mpProductSkuDetailVo : skuList){
//                                for(RuleLevelProductPrice ruleLevelProductPrice : ruleLevelProductPriceList){
//                                    if(null != mpProductSkuDetailVo.getProductSkuId() && null != ruleLevelProductPrice.getProductSkuId() && mpProductSkuDetailVo.getProductSkuId().equals(ruleLevelProductPrice.getProductSkuId())){
//                                        mpProductSkuDetailVo.setMemberPrice(ruleLevelProductPrice.getPlatformPickUpPrice());
//                                        mpProductSkuDetailVo.setDropMemberPrice(ruleLevelProductPrice.getPlatformDropShippingPrice());
//                                        continue loop;
//                                    }
//                                }
//                            }
//                        }
//                    }
//
//                }
//            }
//
//        }

        vo.setSkuList(skuList);
        return R.ok(vo);
    }

    /**
     * 获取批发商品详情
     *
     * @param productCode
     */
    @Override
    public R<MpWholesaleProductDetailVo> getWholesaleProductDetail(String productCode) {
        //用户已登录，判断是否已收藏
        boolean login = StpUtil.isLogin();
        LoginUser loginUser = null;
        TenantType tenantType = null;
        if (login) {
            loginUser = LoginHelper.getLoginUser();
            tenantType = loginUser.getTenantTypeEnum();
        }

        Product product = iProductService.queryByProductCodeNotTenant(productCode);
        if (product == null || ObjUtil.notEqual(product.getShelfState(), ShelfStateEnum.OnShelf)) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }

        Long productId = product.getId();
        ProductSkuAttachmentVo firstImageVo = iProductSkuAttachmentService.queryFirstImageByProductId(productId);
        String productNotice = iShippingReturnsService.getDefaultShippingReturnsContentsBySupplierId(product.getTenantId());

        List<ProductAttachment> productAttachments = iProductAttachmentService.queryByProductIdAndType(productId, AttachmentTypeEnum.File);
        List<ProductAttribute> productAttributes = iProductAttributeService.queryByProductIdAndAttributeType(productId, AttributeTypeEnum.OptionalSpec);

        // 处理sku信息
        List<ProductSku> productSkus = null;
        if (ObjectUtil.isNull(tenantType) || TenantType.Distributor.equals(tenantType)) {
            //游客和分销商查看的是已通过审核的商品
            productSkus = iProductSkuService.getAcceptedAndOnShelfProductSkuListByProductId(product.getId());
        } else {
            //供应商和员工查看的是未审核拒绝的商品
            productSkus = iProductSkuService.getNotRejectedProductSkuListByProductId(product.getId());
        }

        // 批发商品不存在
        if (CollUtil.isEmpty(productSkus)) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }

        List<ProductCategory> productCategories = iProductCategoryService.queryCategoryChainById(product.getBelongCategoryId());
        List<MpProductDetailCategoryVo> categoryVoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(productCategories)) {
            categoryVoList.addAll(BeanUtil.copyToList(CollUtil.reverse(productCategories), MpProductDetailCategoryVo.class));
        }

        Long belongCategoryId = product.getBelongCategoryId();
        MpWholesaleProductDetailVo vo = new MpWholesaleProductDetailVo();
        vo.setName(product.getName());
        vo.setProductCode(product.getProductCode());
        vo.setFirstImageShowUrl(firstImageVo.getAttachmentShowUrl());
        vo.setShelfState(product.getShelfState().name());
        vo.setProductNotice(productNotice);
        vo.setDescription(product.getDescription());
        vo.setFavorites(iTenantFavoritesService.inFavorites(product.getProductCode()));
        vo.setCategoryList(categoryVoList);

        // 其他附件
        List<ProductAttachment> files = iProductAttachmentService.queryByProductIdOrderBySortAsc(product.getId(), AttachmentTypeEnum.File);
        List<MpAttachmentVo> otherAttachmentList = BeanUtil.copyToList(files, MpAttachmentVo.class);
        vo.setOtherAttachmentList(otherAttachmentList);

        // 商品属性
        for (ProductAttribute productAttribute : productAttributes) {
            String attributeName = productAttribute.getAttributeName();
            String attributeValue = productAttribute.getAttributeValue();

            if (StrUtil.isBlank(attributeValue)) {
                continue;
            }

            MpProductAttributeVo attributeVo = new MpProductAttributeVo(attributeName, attributeValue, productAttribute.getAttributeValues());
            AttributeTypeEnum attributeType = productAttribute.getAttributeType();
            ReflectUtil.invoke(vo, "add" + attributeType.name(), attributeVo);
        }

        List<ProductWholesaleTieredPrice> wholesaleTieredPrices = iProductWholesaleTieredPriceService.queryListByProductId(product.getId());
        //国外现货商品详情
        ProductWholesaleDetail wholesaleDetail = iProductWholesaleDetailService.queryByProductId(product.getId());
        ProductReviewRecord record = iProductReviewRecordService.queryNotNewProductRecordByProductCode(productCode);
        List<ProductReviewChangeDetail> reviewChangeDetails = null;
        if (ObjectUtil.isEmpty(record) || ObjectUtil.isEmpty(tenantType) || TenantType.Distributor.equals(tenantType)) {
            if (ObjectUtil.isNotNull(wholesaleDetail)){
                vo.setMinimumOrderQuantity(wholesaleDetail.getMinimumQuantity());
            }
        } else {
            if (!ProductVerifyStateEnum.Accepted.name().equals(record.getReviewState())) {
                reviewChangeDetails = iProductReviewChangeDetailService.queryByReviewRecordIdAndCodeAndFieldName(record.getId(), productCode, null, "productWholesaleTieredPrice");
                List<ProductReviewChangeDetail> productWholesaleDetail = iProductReviewChangeDetailService.queryByReviewRecordIdAndCodeAndFieldName(record.getId(), productCode, null, "productWholesaleDetail");
                ProductReviewChangeDetail wholesaleDetail1 =new ProductReviewChangeDetail();
                if (CollUtil.isNotEmpty(productWholesaleDetail)){
                    wholesaleDetail1=productWholesaleDetail.get(0);
                }
                ProductWholesaleDetail productWholesaleDetailEntity = JSONUtil.toBean(wholesaleDetail1.getFieldValueAfter(), ProductWholesaleDetail.class);
                vo.setMinimumOrderQuantity(productWholesaleDetailEntity.getMinimumQuantity());
                if (CollUtil.isNotEmpty(reviewChangeDetails)){
                    String fieldValueAfter = reviewChangeDetails.get(0).getFieldValueAfter();
                    wholesaleTieredPrices = JSONUtil.parseArray(fieldValueAfter).toList(ProductWholesaleTieredPrice.class);
                }
            } else {
                vo.setMinimumOrderQuantity(wholesaleDetail.getMinimumQuantity());
            }
        }
        if (ObjectUtil.isNotNull(wholesaleDetail)){
            vo.setReservedDay(wholesaleDetail.getReservedTime());
            vo.setDepositRatioPercent(NumberUtil.round(NumberUtil.mul(wholesaleDetail.getDepositRatio(), 100), 0) + "%");
            vo.setDepositRatio(wholesaleDetail.getDepositRatio());
            vo.setDeliveryTypeList(wholesaleDetail.getDeliveryType());
        }

        vo.setWholesaleOrderRegulation(businessParameterService.getValueFromString(BusinessParameterType.WHOLESALE_ORDER_REGULATION));

        // 批发价格体系表格
        List<MpWholesaleProductPriceVo> wholesalePriceBodyList = BeanUtil.copyToList(wholesaleTieredPrices, MpWholesaleProductPriceVo.class);
        vo.setWholesalePriceBodyList(wholesalePriceBodyList);

        Integer totalStock = 0;

        List<MpWholesaleProductSkuDetailVo> skuList = new ArrayList<>();
        for (ProductSku productSku : productSkus) {
            String productSkuCode = productSku.getProductSkuCode();
            Long productSkuId = productSku.getId();
            // 批发暂时用不到
//            ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuId(productSkuId);
            ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuId(productSkuId);

            MpWholesaleProductSkuDetailVo skuWholesaleVo = BeanUtil.toBean(productSku, MpWholesaleProductSkuDetailVo.class);
            skuWholesaleVo.setSpecValNames(StrUtil.split(skuWholesaleVo.getSpecValName(), "/"));
            skuWholesaleVo.setTransportMethod(productSkuDetail.getTransportMethod());
            // skuWholesaleVo.setDropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
            // skuWholesaleVo.setPickUpPrice(productSkuPrice.getPlatformPickUpPrice());
            // skuWholesaleVo.setMsrp(productSkuPrice.getMsrp());

            BigDecimal length = productSkuDetail.getLength();
            BigDecimal width = productSkuDetail.getWidth();
            BigDecimal height = productSkuDetail.getHeight();
            LengthUnitEnum lengthUnit = productSkuDetail.getLengthUnit();

            BigDecimal weight = productSkuDetail.getWeight();
            WeightUnitEnum weightUnit = productSkuDetail.getWeightUnit();

            BigDecimal packLength = productSkuDetail.getPackLength();
            BigDecimal packWidth = productSkuDetail.getPackWidth();
            BigDecimal packHeight = productSkuDetail.getPackHeight();
            LengthUnitEnum packLengthUnit = productSkuDetail.getPackLengthUnit();

            BigDecimal packWeight = productSkuDetail.getPackWeight();
            WeightUnitEnum packWeightUnit = productSkuDetail.getPackWeightUnit();

            StringBuilder sizeBuilder = new StringBuilder().append(length).append(" x ").append(width).append(" x ")
                                                           .append(height).append(" ").append(lengthUnit.name());
            StringBuilder packSizeBuilder = new StringBuilder().append(packLength).append(" x ").append(packWidth)
                                                               .append(" x ").append(packHeight).append(" ")
                                                               .append(packLengthUnit.name());
            StringBuilder weightBuilder = new StringBuilder().append(weight).append(" ").append(weightUnit.name());
            StringBuilder packWeightBuilder = new StringBuilder().append(packWeight).append(" ")
                                                                 .append(packWeightUnit.name());

            skuWholesaleVo.setSize(sizeBuilder.toString());
            skuWholesaleVo.setWeight(weightBuilder.toString());
            skuWholesaleVo.setPackSize(packSizeBuilder.toString());
            skuWholesaleVo.setPackWeight(packWeightBuilder.toString());

            //分销商和游客查看的信息
            if (ObjectUtil.isEmpty(record) || ObjectUtil.isNull(tenantType) || TenantType.Distributor.equals(tenantType)) {
                //价格信息
                List<ProductSkuWholesalePrice> wholesalePrices = iProductSkuWholesalePriceService.queryListByProductSkuId(productSku.getId());
                if (CollUtil.isNotEmpty(wholesalePrices)) {
                    List<Long> tieredPriceIds = wholesalePrices.stream().map(ProductSkuWholesalePrice::getTieredPriceId)
                                                               .distinct().collect(Collectors.toList());
                    List<ProductWholesaleTieredPriceVo> tieredPrices = iProductWholesaleTieredPriceService.queryListByIds(tieredPriceIds);
                    List<ProductSkuWholesaleVo.TieredPrice> tieredPricesList = new ArrayList<>();
                    for (ProductWholesaleTieredPriceVo tp : tieredPrices) {
                        ProductSkuWholesalePrice wholesalePrice = wholesalePrices.stream()
                                                                                 .filter(wp -> ObjectUtil.equals(wp.getProductSkuId(), productSku.getId()) && ObjectUtil.equals(wp.getTieredPriceId(), tp.getId()))
                                                                                 .collect(Collectors.toList()).get(0);
                        ProductSkuWholesaleVo.TieredPrice tieredPrice = new ProductSkuWholesaleVo.TieredPrice();
                        tieredPrice.setMinimumQuantity(tp.getMinimumQuantity());
                        tieredPrice.setPlatformUnitPrice(wholesalePrice.getPlatformUnitPrice());
                        tieredPrice.setOriginUnitPrice(wholesalePrice.getOriginUnitPrice());
                        BigDecimal depositUnitPricePlatform = NumberUtil.mul(wholesalePrice.getPlatformUnitPrice(),
                            wholesaleDetail.getDepositRatio()).setScale(2, RoundingMode.HALF_UP);
                        BigDecimal depositUnitPrice = NumberUtil.mul(wholesalePrice.getOriginUnitPrice(),
                            wholesaleDetail.getDepositRatio()).setScale(2, RoundingMode.HALF_UP);
                        tieredPrice.setDepositUnitPrice(depositUnitPrice);
                        tieredPrice.setPlatformDepositUnitPrice(depositUnitPricePlatform);
                        tieredPrice.setEstimatedOperationalFee(tp.getEstimatedOperationFee());
                        tieredPrice.setEstimatedShoppingFee(tp.getEstimatedShippingFee());
                        tieredPrice.setDealDate(tp.getEstimatedHandleTime());
                        tieredPricesList.add(tieredPrice);
                    }
                    tieredPricesList = tieredPricesList.stream()
                                                       .sorted(Comparator.comparing(ProductSkuWholesaleVo.TieredPrice::getMinimumQuantity))
                                                       .collect(Collectors.toList());
                    skuWholesaleVo.setTieredPricesList(tieredPricesList);
                } else {//如果没有有效的价格信息则不返回该sku信息
                    continue;
                }
            } else {//员工和供应商查看的信息
                List<ProductSkuWholesaleVo.TieredPrice> tieredPricesList = new ArrayList<>();
                for (ProductWholesaleTieredPrice tp : wholesaleTieredPrices) {
                    List<ProductSkuWholesalePrice> wholesalePriceEntities = tp.getWholesalePrices();
                    ProductSkuWholesalePrice wholesalePrice = null;
                    if (CollUtil.isEmpty(wholesalePriceEntities)) {
                        wholesalePrice = iProductSkuWholesalePriceService.getOneByProductSkuIdAndTieredPriceId(productSku.getId(), tp.getId());
                    } else {
                        wholesalePrice = wholesalePriceEntities.stream()
                                                               .filter(wp -> ObjectUtil.equals(wp.getProductSkuId(), productSku.getId()))
                                                               .collect(Collectors.toList()).get(0);
                    }
                    ProductSkuWholesaleVo.TieredPrice tieredPrice = new ProductSkuWholesaleVo.TieredPrice();
                    tieredPrice.setMinimumQuantity(tp.getMinimumQuantity());
                    if (ObjectUtil.isNotNull(wholesalePrice)){
                        tieredPrice.setPlatformUnitPrice(wholesalePrice.getPlatformUnitPrice());
                        tieredPrice.setOriginUnitPrice(wholesalePrice.getOriginUnitPrice());
                    }else {
                        tieredPrice.setPlatformUnitPrice(BigDecimal.ZERO);
                        tieredPrice.setOriginUnitPrice(BigDecimal.ZERO);
                    }

                    if (ObjectUtil.isNotNull(wholesaleDetail)){
                        BigDecimal depositUnitPricePlatform = NumberUtil.mul(wholesalePrice.getPlatformUnitPrice(),
                            wholesaleDetail.getDepositRatio()).setScale(2, RoundingMode.HALF_UP);
                        BigDecimal depositUnitPrice = NumberUtil.mul(wholesalePrice.getOriginUnitPrice(),
                            wholesaleDetail.getDepositRatio()).setScale(2, RoundingMode.HALF_UP);
                        tieredPrice.setDepositUnitPrice(depositUnitPrice);
                        tieredPrice.setPlatformDepositUnitPrice(depositUnitPricePlatform);
                    }else {
                        tieredPrice.setDepositUnitPrice(BigDecimal.ZERO);
                        tieredPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
                    }
                    tieredPrice.setEstimatedOperationalFee(tp.getEstimatedOperationFee());
                    tieredPrice.setEstimatedShoppingFee(tp.getEstimatedShippingFee());
                    tieredPrice.setDealDate(tp.getEstimatedHandleTime());
                    tieredPricesList.add(tieredPrice);
                }
                //按最小数量升序
                tieredPricesList = tieredPricesList.stream()
                                                   .sorted(Comparator.comparing(ProductSkuWholesaleVo.TieredPrice::getMinimumQuantity))
                                                   .collect(Collectors.toList());
                skuWholesaleVo.setTieredPricesList(tieredPricesList);
            }

            if (ObjectUtil.isNotNull(tenantType)) {
   //             totalStock += skuWholesaleVo.getStockTotal();
            }

            // SKU附件
            List<ProductSkuAttachment> imageAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Image);
            List<ProductSkuAttachment> videoAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Video);

            if (CollUtil.isNotEmpty(imageAttachmentList)) {
                skuWholesaleVo.setImageShowUrl(imageAttachmentList.get(0).getAttachmentShowUrl());
            }

            skuWholesaleVo.setAttachmentList(BeanUtil.copyToList(imageAttachmentList, MpAttachmentVo.class));
            skuWholesaleVo.setVideoAttachmentList(BeanUtil.copyToList(videoAttachmentList, MpAttachmentVo.class));

            List<WarehouseAddress> warehouseAddresses = iWarehouseAddressService.queryByProductSkuCode(productSkuCode);
            Set<String> shippingFromSet = warehouseAddresses.stream().map(WarehouseAddress::getCountry)
                                                            .collect(Collectors.toSet());
            skuWholesaleVo.setShippingFrom(CollUtil.join(shippingFromSet, ", "));

            // 渠道管控
            boolean allowSale = true;
            if (loginUser != null) {
                String tenantId = loginUser.getTenantId();
                ProductChannelControl allChannelControl =
                    iProductChannelControlService.queryAllChannelControl(productSkuCode);
                if (allChannelControl != null) {
                    JSONArray allowCodes = allChannelControl.getAllowTenantId();
                    if (CollUtil.isNotEmpty(allowCodes)) {
                        allowSale = allowCodes.contains(tenantId);
                    }
                }
            }
            skuWholesaleVo.setAllowSale(allowSale);

            skuList.add(skuWholesaleVo);
        }

        vo.setSkuList(skuList);
        vo.setTotalStock(totalStock);
        return R.ok(vo);
    }

    /**
     * 获取商品问答列表
     *
     * @param productSkuCode
     * @param pageQuery
     */
    @Override
    public TableDataInfo<MpProductQuestionVo> getProductQAPage(String productSkuCode, String question,
                                                               PageQuery pageQuery) throws Exception {


        Page<ProductQuestion> page = iProductQuestionService.queryPageByProductSkuCode(productSkuCode, question, pageQuery.build());
        List<ProductQuestion> questionList = page.getRecords();

        List<MpProductQuestionVo> voList = new ArrayList<>();
        for (ProductQuestion productQuestion : questionList) {
            String tenantId = productQuestion.getTenantId();
            MpProductQuestionVo newVo = BeanUtil.toBean(productQuestion, MpProductQuestionVo.class);

            List<SysUserVo> sysUserVos = iSysUserService.selectTenantUserByRoleAdminNoTenant(tenantId);
            SysUserVo sysUserVo = CollUtil.get(sysUserVos, 0);

            if (sysUserVo != null) {
                String nickName = sysUserVo.getNickName();
                if (StrUtil.isNotBlank(nickName)) {
                    newVo.setCustomer(StrUtil.hide(nickName, 1, nickName.length() - 1));
                } else {
                    newVo.setCustomer("Anonymous");
                }
            }

            List<ProductQuestionAnswer> answerList = iProductQuestionService.queryAnswerListByQuestionId(productQuestion.getId());
            LinkedList<MpProductAnswerVo> answerVoList = new LinkedList<>();

            if (CollUtil.isNotEmpty(answerList)) {
                for (ProductQuestionAnswer answer : answerList) {
                    MpProductAnswerVo newAnswerVo = BeanUtil.toBean(answer, MpProductAnswerVo.class);

                    String answerTenantId = answer.getTenantId();
                    SysUserVo answerSysUserVo;
                    if (StrUtil.equals(answerTenantId, "000000")) {
                        answerSysUserVo = CollUtil.get(iSysUserService.selectTenantUserBySuperadminNoTenant(answerTenantId), 0);
                    } else {
                        answerSysUserVo = CollUtil.get(iSysUserService.selectTenantUserByRoleAdminNoTenant(answerTenantId), 0);
                    }

                    if (answerSysUserVo != null) {
                        String answerNickName = answerSysUserVo.getNickName();
                        if (StrUtil.isNotBlank(answerNickName)) {
                            newAnswerVo.setAnswerer(StrUtil.hide(answerNickName, 1, answerNickName.length() - 1));
                        } else {
                            newAnswerVo.setAnswerer("Anonymous");
                        }
                    }
                    answerVoList.add(newAnswerVo);
                }
            }
            newVo.setAnswerList(answerVoList);
            voList.add(newVo);
        }
        return TableDataInfo.build(voList, page.getTotal());
    }

    /**
     * 分销商参加商品活动
     *
     * @return
     */
//    @Override
//    public R<Void> participateProductActivity(ParticipateActivityBo bo) {
//        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
//        SpringUtils.context().publishEvent(new CheckPaymentPasswordEvent(bo.getPaymentPassword()));
//        String distributorId = loginUser.getTenantId();
//
//        // 检查是否完善信息
//        ZSMallSystemEventUtils.checkDisInfoPerfection(distributorId);
//
//        String activityID = bo.getActivityID();
//        String activityType = bo.getActivityType();
//        List<ActivityStockBo> bo_stockList = bo.getStockList();
//
//        ProductActivityTypeEnum activityTypeEnum = ProductActivityTypeEnum.valueOf(activityType);
//
//        //查询活动列表
//        ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder()
//                                                                                                  .activityCode(activityID)
//                                                                                                  .build());
//        if (productActivity == null) {
//            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
//        }
//
//        ProductActivityStateEnum activityState = productActivity.getActivityState();
//        if (!ProductActivityStateEnum.Published.equals(activityState) && !ProductActivityStateEnum.InProgress.equals(activityState)) {
//            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
//        }
//
//        String supplierId = productActivity.getTenantId();
//        Long productActivityId = productActivity.getId();
//
//        List<ProductActivityStock> stockList = iProductActivityStockService.queryByProductActivityIdNotTenant(productActivityId);
//        List<String> warehouseSystemCode = stockList.stream().map(ProductActivityStock::getWarehouseSystemCode)
//                                                    .collect(Collectors.toList());
//
//        Integer needQuantityTotal = 0;
//        List<String> bo_warehouseSystemCode = new ArrayList<>();
//        for (ActivityStockBo stockBo : bo_stockList) {
//            needQuantityTotal += stockBo.getQuantity();
//            bo_warehouseSystemCode.add(stockBo.getWarehouseSystemCode());
//        }
//
//        if (!warehouseSystemCode.containsAll(bo_warehouseSystemCode)) { //如果不包含则返回不存在的仓库
//            bo_warehouseSystemCode.remove(warehouseSystemCode);
//            return R.fail(ZSMallStatusCodeEnum.ACTIVITY_WAREHOUSE_NOT_EXIST_ERROR.args(CollUtil.join(bo_warehouseSystemCode, ", ")));
//        }
//
//        ActivityLockDTO.ActivityLockDTOBuilder builder = ActivityLockDTO.builder();
//        builder.distributorId(distributorId)
//               .supplierId(supplierId)
//               .stockList(stockList)
//               .reqStockList(bo_stockList)
//               .quantityRequired(needQuantityTotal)
//               .activity(productActivity);
//
//        if (ProductActivityTypeEnum.StockLock.equals(activityTypeEnum)) {
//            ProductActivityStockLock stockLock = iProductActivityStockLockService.queryByProductActivityId(productActivityId);
//            builder.quantityMinimum(stockLock.getStockLockQuantityMinimum());
//            builder.stockLock(stockLock);
//        } else if (ProductActivityTypeEnum.Buyout.equals(activityTypeEnum)) {
//            ProductActivityBuyout buyout = iProductActivityBuyoutService.queryByProductActivityId(productActivityId);
//            builder.quantityMinimum(buyout.getBuyoutQuantityMinimum());
//            builder.buyout(buyout);
//        }
//
//        //新增分销商锁货信息 和 供应商锁货信息
//        productActivitySupport.lockParticipateActivity(builder.build());
//        return R.ok();
//    }

    /**
     * 查询商品可参与的活动
     *
     * @param productSkuCode
     */
//    @Override
//    public R<MpProductActivityVo> queryMpActivity(String productSkuCode) {
//        MpProductActivityVo vo = new MpProductActivityVo();
//
//        ProductActivityTypeEnum[] values = ProductActivityTypeEnum.values();
//        for (ProductActivityTypeEnum value : values) {
//            List<MpProductActivitySelectVo> mpProductActivitySelectVos = iProductActivityService.queryMpProductActivitySelect(null, productSkuCode, value);
//            ReflectUtil.setFieldValue(vo, value.name(), mpProductActivitySelectVos);
//        }
//        return R.ok(vo);
//    }

    /**
     * 获取商品Sku简单详情
     *
     * @param productSkuCode
     * @return
     */
    @Override
    public ProductSkuSimpleVo getProductSkuSimpleDetail(String productSkuCode) {
        ProductSku productSku = iProductSkuService.queryByProductSkuCodeWithNotTenant(productSkuCode);
        if (productSku == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
        }

        String productCode = productSku.getProductCode();
        String sku = productSku.getSku();
        String name = productSku.getName();

        ProductSkuSimpleVo skuSimpleVo = new ProductSkuSimpleVo(productCode, productSkuCode, name, sku);

        ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductSkuCode(productSkuCode);
        skuSimpleVo.setProductSkuAttachment(productSkuAttachmentVo);

        return skuSimpleVo;
    }

    /**
     * 加入收藏夹
     *
     * @param bo
     */
    @Override
    public R<Void> addToFavorites(AddToFavoritesBo bo) {
        if (!LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)){
            return R.fail("非分销商不支持收藏");
        }
        String productCode = bo.getProductCode();
        String productSkuCode = bo.getProductSkuCode();
        if (bo.getIsProductCode()) {
            if (StrUtil.isBlank(productCode)) {
                return R.fail("productCode不能为空!");
            }
        } else {
            if (StrUtil.isBlank(productSkuCode)) {
                return R.fail("SkuId不能为空!");
            }
        }
        boolean redisSpu=false;
        //收藏SPU
        if (bo.getCollected()){
            List<ProductSku> list=new ArrayList<>();
            if (bo.getIsProductCode()){
                boolean b =  TenantHelper.ignore(()->iProductService.existProductCode(String.valueOf(bo.getProductCode())));
                if (!b){
                    return R.fail("productCode不存在"+productCode);
                }
                //构建收藏夹对象
                LambdaQueryWrapper<ProductSku> o = new LambdaQueryWrapper<>();
                o.eq(ProductSku::getProductCode,productCode);
                list =TenantHelper.ignore(()->iProductSkuService.list(o));
                //删除当前租户的SPU收藏
                TenantHelper.ignore(()-> tenantFavoritesMapper.deleteBySpu(productCode,LoginHelper.getTenantId()));
                redisSpu=true;
            }else {
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                if (ObjectUtil.isNull(productSku)){
                    return R.fail("SkuID不存在"+productSkuCode);
                }
                TenantHelper.ignore(()-> tenantFavoritesMapper.deleteBySku(productSkuCode,LoginHelper.getTenantId()));
                list.add(productSku);
                //判断当前SKU下面已经收藏的商品
                LambdaQueryWrapper<TenantFavorites> q = new LambdaQueryWrapper<>();
                q.eq(TenantFavorites::getProductCode,productSku.getProductCode());
                List<TenantFavorites> tenantFavorites = iTenantFavoritesService.getBaseMapper().selectList(q);
                //查询当前SKU下面有的商品
                LambdaQueryWrapper<ProductSku> p = new LambdaQueryWrapper<>();
                p.eq(ProductSku::getProductCode,productSku.getProductCode());
                p.eq(ProductSku::getShelfState,ShelfStateEnum.OnShelf);
                List<ProductSku> productSkus = TenantHelper.ignore(() -> iProductSkuService.getBaseMapper().selectList(p));
                redisSpu= productSkus.size() == tenantFavorites.size() + 1;
            }
            //查询站点
            List<SiteCountryCurrency> sites = siteCountryCurrencyMapper.selectList();
            sites = sites.stream().filter(site -> !"CA".equals(site.getCountryCode()))
                         .collect(Collectors.toList());

            String cacheMapValue = sysConfigService.selectConfigByKey("Generate_Data_With_CN_Currency_Code");
            List<String> ignore= JSONUtil.toList(cacheMapValue, String.class);
            String tenantId = LoginHelper.getTenantId();
            boolean isIgnoredTenant = ignore.contains(tenantId);
            List<SiteCountryCurrency> finalSites = sites;
            list.forEach(s -> {
                finalSites.forEach(site -> {
                    //如果是配置的租户，并且是CountryCode=‘CN’，则生成CN数据,否则不生成CN数据
                    if (isIgnoredTenant || !"CN".equals(site.getCountryCode())) {
                        TenantFavorites te = new TenantFavorites();
                        te.setProductId(s.getProductId());
                        te.setProductCode(s.getProductCode());
                        te.setProductSkuId(s.getId());
                        te.setProductSkuCode(s.getProductSkuCode());
                        te.setSite(site.getCountryCode());
                        te.setCurrency(site.getCurrencySymbol());
                        iTenantFavoritesService.save(te);
                    }
                });
            });

        }  else {
            //取消
            if (bo.getIsProductCode()){
                LambdaUpdateWrapper<TenantFavorites> o = new LambdaUpdateWrapper<>();
                o.set(TenantFavorites::getDelFlag,1);
                o.eq(TenantFavorites::getProductCode,productCode);
                iTenantFavoritesService.update(o);
            }else {
                LambdaUpdateWrapper<TenantFavorites> o = new LambdaUpdateWrapper<>();
                o.set(TenantFavorites::getDelFlag,1);
                o.eq(TenantFavorites::getProductSkuCode,productSkuCode);
                iTenantFavoritesService.update(o);
            }

        }

        if (redisSpu){
            RedisUtils.setCacheObject(GlobalConstants.FAVORITES_SPU+LoginHelper.getTenantId()+productCode,true);
        }else {
            RedisUtils.deleteKeys(GlobalConstants.FAVORITES_SPU+LoginHelper.getTenantId()+productCode);
        }
        return R.ok();
    }

    @Override
    public R<Void> addToFavoritesV2(AddToFavoritesBo bo) {
        LoginHelper.getLoginUser(TenantType.Distributor);

        String productCode = bo.getProductCode();
        String productSkuCode = bo.getProductSkuCode();

        if (StrUtil.isAllBlank(productCode, productSkuCode)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        if (StrUtil.isNotBlank(productSkuCode)) {
            ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
            if (productSku == null) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
            }

            productCode = productSku.getProductCode();
        }

        Boolean collected = bo.getCollected();
        LoginUser loginUser = LoginHelper.getLoginUser();
        String tenantId ="000000";
        if(ObjectUtil.isNotEmpty(loginUser)){
            tenantId = loginUser.getTenantId();
        }

        List<TenantFavorites> tenantFavorites = iTenantFavoritesService.createByProductCode(productCode,tenantId);
        if (collected) {
            //productCode && tenantId  先del 再insert
            iTenantFavoritesService.removeByProductCodeAndTenantId(productCode,tenantId);
            iTenantFavoritesService.saveBatch(tenantFavorites);
        } else {  // 取消收藏，删除收藏夹
            if (tenantFavorites != null) {
                iTenantFavoritesService.removeByProductCodeAndTenantId(productCode,tenantId);
            }
        }
        return null;

    }

    private List<MpProductVo> convertToMpProductVo(LoginUser loginUser, List<SearchHit> hitList) {
//        List<String> favoritesProductCodes = new ArrayList<>();
//        if (loginUser != null) {
//            // 收藏的商品编号
//            favoritesProductCodes = iTenantFavoritesService.queryAllProductCode();
//        }

        List<MpProductVo> productVoList = new ArrayList<>();
        List<Long> productSkuIdList = new ArrayList<>();
//        String ruleCustomizerTenantId = null;
        for (SearchHit searchHit : hitList) {
            String id = searchHit.getId();
            String sourceAsString = searchHit.getSourceAsString();
            MpProductVo mpProductVo = JSONUtil.toBean(sourceAsString, MpProductVo.class);
            mpProductVo.setProductSkuCode(id);
            // 库存要展示spu总库存 根据
            String productCode = mpProductVo.getProductCode();
            SpuStock spuStock =TenantHelper.ignore(()->iProductSkuStockService.getBaseMapper().getSpuStock(productCode));
            if (ObjectUtil.isNotNull(spuStock)){
                mpProductVo.setPickUpStockTotal(spuStock.getPickUpStockTotal());
                mpProductVo.setDropShippingStockTotal(spuStock.getDropShippingStockTotal());
            }else {
                mpProductVo.setPickUpStockTotal(0);
                mpProductVo.setDropShippingStockTotal(0);
            }
            //   List<ProductSku> productSkus =TenantHelper.ignore(()->iProductSkuService.list(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductCode, productCode))) ;
            //productSkus 集合  stream 方法 统计出集合内 ProductSku.stockTotal 的总和
          //  log.info("productSkus:{}",productSkus);
//            Integer stockTotal = productSkus.stream()
//                                            .filter(sku -> sku.getStockTotal() != null) // 过滤掉getStockTotal返回null的ProductSku
//                                            .mapToInt(ProductSku::getStockTotal) // 映射为int类型
//                                            .sum();
//            Optional<ProductSku> productSkuOptional = productSkus.stream()
//                                               .filter(i -> i.getProductSkuCode().equals(id)) // 过滤出id匹配的对象
//                                               .findFirst(); // 获取第一个匹配的对象
//            if(productSkuOptional.isPresent()){
//                productSkuIdList.add(productSkuOptional.get().getId());
//                mpProductVo.setProductSkuId(productSkuOptional.get().getId());
//                if(StringUtils.isEmpty(ruleCustomizerTenantId)){
//                    ruleCustomizerTenantId = productSkuOptional.get().getTenantId();
//                }
//            }

            mpProductVo.setCollected(RedisUtils.hasKey(GlobalConstants.FAVORITES_SPU + LoginHelper.getTenantId() + productCode));
//            if (CollUtil.isNotEmpty(favoritesProductCodes)) {
//                mpProductVo.setCollected(CollUtil.contains(favoritesProductCodes, mpProductVo.getProductCode()));
//            }
            productVoList.add(mpProductVo);
        }
        String tenantId = LoginHelper.getTenantId();

        List<String> codes = productVoList.stream().map(item -> item.getProductCode()).collect(Collectors.toList());
        List<Product> productList = TenantHelper.ignore(()->productService.list(new LambdaQueryWrapper<Product>().in(Product::getProductCode, codes).eq(Product::getDelFlag,0)));
        Map<String, Product> productMap = productList.stream()
                                                  .collect(Collectors.toMap(key -> key.getProductCode(), value -> value));
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        // 当前租户标识---查外部 查内部需要额外接口
        for (MpProductVo mpProductVo : productVoList) {
            String productCode = mpProductVo.getProductCode();
            if(productMap.containsKey(productCode)){
                Product product = productMap.get(productCode);
                String ruleCustomizerTenantId = product.getTenantId();
                // 一个分销商在一个供应商只有一个等级
                if(TenantType.Distributor.equals(tenantTypeEnum)){
                    MemberRuleRelation one = memberRuleRelationService.getOne(new LambdaQueryWrapper<MemberRuleRelation>()
                        .eq(MemberRuleRelation::getRuleCustomizerTenantId, ruleCustomizerTenantId)
                        .eq(MemberRuleRelation::getRuleFollowerTenantId, tenantId)
                        .eq(MemberRuleRelation::getDelFlag,0));
                    if (ObjectUtil.isNotEmpty(one)){
                        Long levelId = one.getLevelId();
                        MemberLevel level = TenantHelper.ignore(()->iMemberLevelService.getById(levelId));
                        if(level.getStatus().equals(0)){
                            LambdaQueryWrapper<RuleLevelProductPrice> levelProductPriceLambdaQueryWrapper = new LambdaQueryWrapper<>();
                            levelProductPriceLambdaQueryWrapper.eq(RuleLevelProductPrice::getRuleCustomizerTenantId,ruleCustomizerTenantId)
                                                               .eq(RuleLevelProductPrice::getLevelId,levelId)
                                                               .in(RuleLevelProductPrice::getProductId,product.getId())
                                                               .eq(RuleLevelProductPrice::getDelFlag,0);
                            // spu纬度 pc商场显示
                            List<RuleLevelProductPrice> ruleLevelProductPriceList = TenantHelper.ignore(() -> ruleLevelProductPriceService.list(levelProductPriceLambdaQueryWrapper));
                            Optional<BigDecimal> minPlatformPickUpPrice = ruleLevelProductPriceList.stream()
                                                                                                   .map(RuleLevelProductPrice::getPlatformPickUpPrice)
                                                                                                   .filter(Objects::nonNull)
                                                                                                   .min(Comparator.naturalOrder());

                            Optional<BigDecimal> minDropPickUpPrice = ruleLevelProductPriceList.stream()
                                                                                               .map(RuleLevelProductPrice::getPlatformDropShippingPrice)
                                                                                               .filter(Objects::nonNull)
                                                                                               .min(Comparator.naturalOrder());
                            minPlatformPickUpPrice.ifPresent(mpProductVo::setMemberPrice);
                            minDropPickUpPrice.ifPresent(mpProductVo::setDropMemberPrice);
                        }
                    }
                }
                //如果用户未登录查询不到库存和价格
                if (ObjUtil.isNull(loginUser)){
                    mpProductVo.setPickUpStockTotal(null);
                    mpProductVo.setDropShippingStockTotal(null);
                    mpProductVo.setMainPrice(null);
                }
            }
        }

        return productVoList;
    }

    /**
     * 递归处理聚合查询分类
     */
    private List<MpProductCategoryVo> recursionAggsCategory(String language, Aggregation aggregation, String keyName,
                                                            int count) {
        List<MpProductCategoryVo> voList = new ArrayList<>();
        List<? extends Terms.Bucket> buckets = ((Terms) aggregation).getBuckets();
        if (CollUtil.isNotEmpty(buckets)) {
            for (Terms.Bucket bucket : buckets) {
                Object key = bucket.getKey();
                MpProductCategoryVo productCategoryBody = categoryAggsToRespBody(language, NumberUtil.parseLong(key.toString(), null));
                if (productCategoryBody == null) {
                    continue;
                }

                int newCount = count + 1;
                Aggregation nextCategory = bucket.getAggregations().get(keyName + newCount);
                if (nextCategory != null) {
                    List<? extends Terms.Bucket> children = ((Terms) aggregation).getBuckets();
                    if (CollUtil.isNotEmpty(children)) {
                        List<MpProductCategoryVo> childrenBodies = recursionAggsCategory(language, nextCategory, keyName, newCount);
                        if (CollUtil.isNotEmpty(childrenBodies)) {
                            productCategoryBody.setHasChildren(true);
                            productCategoryBody.setChildren(childrenBodies);
                            productCategoryBody.setDisabled(true);
                        }
                    }
                }
                voList.add(productCategoryBody);
            }
        }
        return voList;
    }

    /**
     * 封装分类数据
     */
    private MpProductCategoryVo categoryAggsToRespBody(String language, Long categoryId) {
        MpProductCategoryVo categoryVo = null;
        if (categoryId != null) {
            ProductCategoryVo productCategory = iProductCategoryService.queryById(categoryId);
            if (productCategory != null) {
                categoryVo = BeanUtil.toBean(productCategory, MpProductCategoryVo.class);
            }
        }
        return categoryVo;
    }

    @Override
    public void dealTenantFavoritesData() {
        List<SiteCountryCurrency> sites = siteCountryCurrencyMapper.selectList();
        sites = sites.stream()
                     .filter(site -> !"US".equals(site.getCountryCode()))
                     .collect(Collectors.toList());
        String cacheMapValue = sysConfigService.selectConfigByKey("Generate_Data_With_CN_Currency_Code");
        List<String> ignore= JSONUtil.toList(cacheMapValue, String.class);
        LambdaQueryWrapper<TenantFavorites> q = new LambdaQueryWrapper<>();
        List<TenantFavorites> tenantFavorites =TenantHelper.ignore(()->tenantFavoritesMapper.selectList(q));
        List<SiteCountryCurrency> finalSites = sites;
        tenantFavorites.forEach(s->{
            boolean isIgnoredTenant = ignore.contains(s.getTenantId());
            finalSites.forEach(site -> {
                //如果是配置的租户，并且是CountryCode=‘CN’，则生成CN数据,否则不生成CN数据
                if (isIgnoredTenant || !"CN".equals(site.getCountryCode())) {
                    TenantFavorites te = new TenantFavorites();
                    te.setProductId(s.getProductId());
                    te.setTenantId(s.getTenantId());
                    te.setProductCode(s.getProductCode());
                    te.setProductSkuId(s.getProductSkuId());
                    te.setProductSkuCode(s.getProductSkuCode());
                    te.setSite(site.getCountryCode());
                    te.setCurrency(site.getCurrencySymbol());
                    iTenantFavoritesService.save(te);
                }
            });
        });
        log.info("收藏夹数据清洗完成");

    }
}
