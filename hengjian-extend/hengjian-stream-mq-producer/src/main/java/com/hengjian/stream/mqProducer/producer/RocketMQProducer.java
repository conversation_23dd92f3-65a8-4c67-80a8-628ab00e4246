//package com.hengjian.stream.mq.producer;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.poi.ss.formula.functions.T;
//import org.apache.rocketmq.client.producer.SendResult;
//import org.apache.rocketmq.spring.core.RocketMQTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.messaging.Message;
//import org.springframework.messaging.MessageHeaders;
//import org.springframework.messaging.support.MessageBuilder;
//import org.springframework.stereotype.Component;
//import org.springframework.util.MimeTypeUtils;
//
//
///**
// * RocketMQ 生产者
// */
//@Slf4j
//@Component
//public class RocketMQProducer {
//
//    @Autowired
//    private RocketMQTemplate rocketMqTemplate;
//
//
//    /**
//     * 延迟/定时消息
//     *
//     * @param topic            主题
//     * @param msgBody          消息内容
//     * @param deliverTimeMills 延迟/定时 时间戳
//     * @return
//     */
//    public SendResult syncSendDeliverTimeMills(String topic, Object msgBody, long deliverTimeMills) {
//        Message<Object> message = MessageBuilder.withPayload(msgBody)
//            .setHeader(MessageHeaders.CONTENT_TYPE, MimeTypeUtils.APPLICATION_JSON_VALUE)
//            .build();
//        return rocketMqTemplate.syncSendDeliverTimeMills(topic, message, deliverTimeMills);
//    }
//
//}
