package com.hengjian.stream.mqProducer.constants;

import com.hengjian.common.core.constant.GlobalConstants;

/**
 * 队列常量配置
 */
public interface QueueConstants {
    /**
     * redis 队列前缀
     */
    String PREFIX_QUEUE_REDIS = GlobalConstants.GLOBAL_REDIS_KEY + "queue:";

    interface QueueName {
        /**
         * 通知推送
         */
        String NOTICE = PREFIX_QUEUE_REDIS + "notice_publish";

        /**
         * ElasticSearch商品数据推送
         */
        String ES_PRODUCT_PUSH = PREFIX_QUEUE_REDIS + "es_product_push";

        /**
         * 活动过期处理队列
         */
        String ACTIVITY_EXPIRE = PREFIX_QUEUE_REDIS + "activity_expire";
    }
}
